# Use Node.js 20 as the base image
FROM node:20

# Set the working directory inside the container
WORKDIR /app

# Copy dependency definitions
COPY package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the application code
COPY . .

# Copy .env file (if you want it inside the container)
#COPY .env.main .env

# Build the NestJS project (if using TypeScript)
RUN npm run build

# Expose the port the app runs on
EXPOSE 8000

# Run the application
CMD ["node", "dist/main"]
