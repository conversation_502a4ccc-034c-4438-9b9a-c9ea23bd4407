version: "3.8"

services:
  postgres:
    image: postgres:16
    container_name: postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: mypassword
      POSTGRES_DB: postgres
    volumes:
      - ./pgdata:/var/lib/postgresql/data
      - ./postgresql.conf:/etc/postgresql/postgresql.conf
    command:
      [
        "postgres",
        "-c", "logging_collector=on",
        "-c", "log_directory=/var/lib/postgresql/data/log",
        "-c", "log_filename=postgresql.log",
        "-c", "log_rotation_age=1d",
        "-c", "log_rotation_size=20MB",
        "-c", "log_truncate_on_rotation=on"
      ]
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    ports:
      - "5432:5432"