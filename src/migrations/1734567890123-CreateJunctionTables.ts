import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateJunctionTables1734567890123 implements MigrationInterface {
  name = 'CreateJunctionTables1734567890123';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create order_clients junction table
    await queryRunner.createTable(
      new Table({
        name: 'order_clients',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'orderId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'clientId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'isPrimary',
            type: 'boolean',
            default: false,
          },
          {
            name: 'notes',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create order_inspectors junction table
    await queryRunner.createTable(
      new Table({
        name: 'order_inspectors',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'orderId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'inspectorId',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'role',
            type: 'enum',
            enum: ['primary', 'secondary', 'specialist'],
            default: "'primary'",
          },
          {
            name: 'assignedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'assignedBy',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'specialInstructions',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // Create unique indexes
    await queryRunner.createIndex(
      'order_clients',
      new Index('IDX_order_client_unique', ['orderId', 'clientId'], { isUnique: true }),
    );

    await queryRunner.createIndex(
      'order_inspectors',
      new Index('IDX_order_inspector_unique', ['orderId', 'inspectorId'], { isUnique: true }),
    );

    // Create foreign keys for order_clients
    await queryRunner.createForeignKey(
      'order_clients',
      new ForeignKey({
        columnNames: ['orderId'],
        referencedTableName: 'inspection_orders',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'order_clients',
      new ForeignKey({
        columnNames: ['clientId'],
        referencedTableName: 'users',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Create foreign keys for order_inspectors
    await queryRunner.createForeignKey(
      'order_inspectors',
      new ForeignKey({
        columnNames: ['orderId'],
        referencedTableName: 'inspection_orders',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'order_inspectors',
      new ForeignKey({
        columnNames: ['inspectorId'],
        referencedTableName: 'inspectors',
        referencedColumnNames: ['id'],
        onDelete: 'CASCADE',
      }),
    );

    // Migrate existing data from arrays to junction tables
    await this.migrateExistingData(queryRunner);

    // Remove the old array columns
    await queryRunner.dropColumn('inspection_orders', 'clientIds');
    await queryRunner.dropColumn('inspection_orders', 'assignedInspectorIds');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back the array columns
    await queryRunner.addColumn('inspection_orders', {
      name: 'clientIds',
      type: 'int',
      isArray: true,
      isNullable: true,
    });

    await queryRunner.addColumn('inspection_orders', {
      name: 'assignedInspectorIds',
      type: 'int',
      isArray: true,
      isNullable: true,
    });

    // Migrate data back to arrays
    await this.migrateDataBack(queryRunner);

    // Drop junction tables
    await queryRunner.dropTable('order_inspectors');
    await queryRunner.dropTable('order_clients');
  }

  private async migrateExistingData(queryRunner: QueryRunner): Promise<void> {
    // Migrate client data
    const ordersWithClients = await queryRunner.query(`
      SELECT id, "clientIds" 
      FROM inspection_orders 
      WHERE "clientIds" IS NOT NULL AND array_length("clientIds", 1) > 0
    `);

    for (const order of ordersWithClients) {
      if (order.clientIds && order.clientIds.length > 0) {
        for (let i = 0; i < order.clientIds.length; i++) {
          await queryRunner.query(`
            INSERT INTO order_clients ("orderId", "clientId", "isPrimary", "createdAt", "updatedAt")
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `, [order.id, order.clientIds[i], i === 0]);
        }
      }
    }

    // Migrate inspector data
    const ordersWithInspectors = await queryRunner.query(`
      SELECT id, "assignedInspectorIds" 
      FROM inspection_orders 
      WHERE "assignedInspectorIds" IS NOT NULL AND array_length("assignedInspectorIds", 1) > 0
    `);

    for (const order of ordersWithInspectors) {
      if (order.assignedInspectorIds && order.assignedInspectorIds.length > 0) {
        for (let i = 0; i < order.assignedInspectorIds.length; i++) {
          const role = i === 0 ? 'primary' : 'secondary';
          await queryRunner.query(`
            INSERT INTO order_inspectors ("orderId", "inspectorId", "role", "assignedAt", "isActive", "createdAt", "updatedAt")
            VALUES ($1, $2, $3, CURRENT_TIMESTAMP, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          `, [order.id, order.assignedInspectorIds[i], role]);
        }
      }
    }
  }

  private async migrateDataBack(queryRunner: QueryRunner): Promise<void> {
    // Migrate client data back to arrays
    const orders = await queryRunner.query(`
      SELECT DISTINCT "orderId" FROM order_clients
    `);

    for (const order of orders) {
      const clients = await queryRunner.query(`
        SELECT "clientId" FROM order_clients 
        WHERE "orderId" = $1 
        ORDER BY "isPrimary" DESC, "createdAt" ASC
      `, [order.orderId]);

      const clientIds = clients.map(c => c.clientId);
      await queryRunner.query(`
        UPDATE inspection_orders 
        SET "clientIds" = $1 
        WHERE id = $2
      `, [clientIds, order.orderId]);
    }

    // Migrate inspector data back to arrays
    const ordersWithInspectors = await queryRunner.query(`
      SELECT DISTINCT "orderId" FROM order_inspectors WHERE "isActive" = true
    `);

    for (const order of ordersWithInspectors) {
      const inspectors = await queryRunner.query(`
        SELECT "inspectorId" FROM order_inspectors 
        WHERE "orderId" = $1 AND "isActive" = true
        ORDER BY "role" ASC, "createdAt" ASC
      `, [order.orderId]);

      const inspectorIds = inspectors.map(i => i.inspectorId);
      await queryRunner.query(`
        UPDATE inspection_orders 
        SET "assignedInspectorIds" = $1 
        WHERE id = $2
      `, [inspectorIds, order.orderId]);
    }
  }
}
