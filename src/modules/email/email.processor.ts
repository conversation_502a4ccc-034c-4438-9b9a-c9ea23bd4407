import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';

import { EmailService, EmailJob } from './email.service';

@Processor('email')
export class EmailProcessor {
  private readonly logger = new Logger(EmailProcessor.name);

  constructor(private readonly emailService: EmailService) {}

  @Process('send-email')
  async handleSendEmail(job: Job<EmailJob>) {
    this.logger.log(`Processing email job ${job.id} to ${job.data.to}`);
    
    try {
      // Update job progress
      await job.progress(10);
      
      // Process the email
      const result = await this.emailService.processEmail(job.data);
      
      // Update job progress
      await job.progress(100);
      
      this.logger.log(`Email job ${job.id} completed successfully`);
      return result;
    } catch (error) {
      this.logger.error(`Email job ${job.id} failed:`, error.stack);
      throw error;
    }
  }

  @Process('send-bulk-email')
  async handleBulkEmail(job: Job<{ emails: EmailJob[] }>) {
    this.logger.log(`Processing bulk email job ${job.id} with ${job.data.emails.length} emails`);
    
    const results = [];
    const total = job.data.emails.length;
    
    for (let i = 0; i < total; i++) {
      try {
        const result = await this.emailService.processEmail(job.data.emails[i]);
        results.push({ success: true, result });
        
        // Update progress
        const progress = Math.round(((i + 1) / total) * 100);
        await job.progress(progress);
        
      } catch (error) {
        this.logger.error(`Failed to send email ${i + 1} in bulk job ${job.id}:`, error.stack);
        results.push({ success: false, error: error.message });
      }
    }
    
    this.logger.log(`Bulk email job ${job.id} completed. Success: ${results.filter(r => r.success).length}/${total}`);
    return results;
  }
}
