import { Injectable } from '@nestjs/common';
import { Order } from '../entities/order.entity';
import { OrderWithLegacyFields } from '../interfaces/order-with-legacy-fields.interface';

@Injectable()
export class OrderTransformService {
  /**
   * Transform Order entity to include legacy fields for backward compatibility
   */
  transformOrderWithLegacyFields(order: Order): OrderWithLegacyFields {
    const transformed = { ...order } as OrderWithLegacyFields;

    // Extract client IDs and information
    if (order.orderClients) {
      transformed.clientIds = order.orderClients
        .sort((a, b) => {
          // Primary client first, then by creation date
          if (a.isPrimary && !b.isPrimary) return -1;
          if (!a.isPrimary && b.isPrimary) return 1;
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        })
        .map(oc => oc.clientId);

      transformed.clientNames = order.orderClients
        .filter(oc => oc.client)
        .map(oc => oc.client.name);

      const primaryClientRelation = order.orderClients.find(oc => oc.isPrimary);
      if (primaryClientRelation && primaryClientRelation.client) {
        transformed.primaryClient = {
          id: primaryClientRelation.client.id,
          name: primaryClientRelation.client.name,
          email: primaryClientRelation.client.email,
        };
      } else if (order.orderClients.length > 0 && order.orderClients[0].client) {
        // Fallback to first client if no primary is set
        const firstClient = order.orderClients[0].client;
        transformed.primaryClient = {
          id: firstClient.id,
          name: firstClient.name,
          email: firstClient.email,
        };
      }
    } else {
      transformed.clientIds = [];
      transformed.clientNames = [];
    }

    // Extract inspector IDs and information
    if (order.orderInspectors) {
      const activeInspectors = order.orderInspectors.filter(oi => oi.isActive);
      
      transformed.assignedInspectorIds = activeInspectors
        .sort((a, b) => {
          // Primary inspector first, then by creation date
          const roleOrder = { primary: 0, secondary: 1, specialist: 2 };
          if (roleOrder[a.role] !== roleOrder[b.role]) {
            return roleOrder[a.role] - roleOrder[b.role];
          }
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        })
        .map(oi => oi.inspectorId);

      transformed.inspectorNames = activeInspectors
        .filter(oi => oi.inspector)
        .map(oi => oi.inspector.name);

      const primaryInspectorRelation = activeInspectors.find(oi => oi.role === 'primary');
      if (primaryInspectorRelation && primaryInspectorRelation.inspector) {
        transformed.primaryInspector = {
          id: primaryInspectorRelation.inspector.id,
          name: primaryInspectorRelation.inspector.name,
          email: primaryInspectorRelation.inspector.email,
        };
      } else if (activeInspectors.length > 0 && activeInspectors[0].inspector) {
        // Fallback to first inspector if no primary is set
        const firstInspector = activeInspectors[0].inspector;
        transformed.primaryInspector = {
          id: firstInspector.id,
          name: firstInspector.name,
          email: firstInspector.email,
        };
      }
    } else {
      transformed.assignedInspectorIds = [];
      transformed.inspectorNames = [];
    }

    return transformed;
  }

  /**
   * Transform array of orders
   */
  transformOrdersWithLegacyFields(orders: Order[]): OrderWithLegacyFields[] {
    return orders.map(order => this.transformOrderWithLegacyFields(order));
  }

  /**
   * Get client display name for an order
   */
  getClientDisplayName(order: Order): string {
    if (!order.orderClients || order.orderClients.length === 0) {
      return 'No clients assigned';
    }

    const clientNames = order.orderClients
      .filter(oc => oc.client)
      .map(oc => oc.client.name);

    if (clientNames.length === 0) {
      return 'No clients assigned';
    }

    if (clientNames.length === 1) {
      return clientNames[0];
    }

    return `${clientNames[0]} + ${clientNames.length - 1} others`;
  }

  /**
   * Get inspector display name for an order
   */
  getInspectorDisplayName(order: Order): string {
    if (!order.orderInspectors || order.orderInspectors.length === 0) {
      return 'No inspectors assigned';
    }

    const activeInspectors = order.orderInspectors.filter(oi => oi.isActive);
    const inspectorNames = activeInspectors
      .filter(oi => oi.inspector)
      .map(oi => oi.inspector.name);

    if (inspectorNames.length === 0) {
      return 'No inspectors assigned';
    }

    if (inspectorNames.length === 1) {
      return inspectorNames[0];
    }

    return `${inspectorNames[0]} + ${inspectorNames.length - 1} others`;
  }

  /**
   * Get primary client email for an order
   */
  getPrimaryClientEmail(order: Order): string {
    if (!order.orderClients || order.orderClients.length === 0) {
      return '';
    }

    const primaryClient = order.orderClients.find(oc => oc.isPrimary);
    if (primaryClient && primaryClient.client) {
      return primaryClient.client.email;
    }

    // Fallback to first client
    const firstClient = order.orderClients[0];
    if (firstClient && firstClient.client) {
      return firstClient.client.email;
    }

    return '';
  }

  /**
   * Get primary inspector email for an order
   */
  getPrimaryInspectorEmail(order: Order): string {
    if (!order.orderInspectors || order.orderInspectors.length === 0) {
      return '';
    }

    const activeInspectors = order.orderInspectors.filter(oi => oi.isActive);
    const primaryInspector = activeInspectors.find(oi => oi.role === 'primary');
    
    if (primaryInspector && primaryInspector.inspector) {
      return primaryInspector.inspector.email;
    }

    // Fallback to first inspector
    const firstInspector = activeInspectors[0];
    if (firstInspector && firstInspector.inspector) {
      return firstInspector.inspector.email;
    }

    return '';
  }
}
