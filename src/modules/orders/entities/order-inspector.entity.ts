import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Order } from './order.entity';
import { Inspector } from '../../inspectors/entities/inspector.entity';

export enum InspectorRole {
  PRIMARY = 'primary',
  SECONDARY = 'secondary',
  SPECIALIST = 'specialist',
}

@Entity('order_inspectors')
@Index(['orderId', 'inspectorId'], { unique: true })
export class OrderInspector {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  orderId: number;

  @Column()
  inspectorId: number;

  @Column({
    type: 'enum',
    enum: InspectorRole,
    default: InspectorRole.PRIMARY,
  })
  role: InspectorRole;

  @Column({ nullable: true })
  assignedAt: Date;

  @Column({ nullable: true })
  assignedBy: number; // User ID who assigned

  @Column({ nullable: true, type: 'text' })
  specialInstructions: string;

  @Column({ default: true })
  isActive: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Order, (order) => order.orderInspectors, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'orderId' })
  order: Order;

  @ManyToOne(() => Inspector, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'inspectorId' })
  inspector: Inspector;
}
