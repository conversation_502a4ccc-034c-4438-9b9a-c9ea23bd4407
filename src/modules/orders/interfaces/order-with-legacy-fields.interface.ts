import { Order } from '../entities/order.entity';

/**
 * Interface to extend Order with legacy fields for backward compatibility
 */
export interface OrderWithLegacyFields extends Order {
  // Virtual properties for backward compatibility
  clientIds?: number[];
  assignedInspectorIds?: number[];
  
  // Helper properties for display
  primaryClient?: {
    id: number;
    name: string;
    email: string;
  };
  
  primaryInspector?: {
    id: number;
    name: string;
    email: string;
  };
  
  clientNames?: string[];
  inspectorNames?: string[];
}
