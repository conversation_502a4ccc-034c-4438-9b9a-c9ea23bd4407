import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { CustomFieldsService } from './custom-fields.service';
import { CreateCustomFieldDto } from './dto/create-custom-field.dto';
import { UpdateCustomFieldDto } from './dto/update-custom-field.dto';
import { CustomFieldQueryDto } from './dto/custom-field-query.dto';
import { SetCustomFieldValueDto } from './dto/set-custom-field-value.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';

@ApiTags('Custom Fields')
@Controller('custom-fields')
export class CustomFieldsController {
  constructor(private readonly customFieldsService: CustomFieldsService) {}

  @Post()
  @Auth('admin')
  @ApiOperation({ summary: 'Create a new custom field' })
  @ApiResponse({ status: 201, description: 'Custom field successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createCustomFieldDto: CreateCustomFieldDto) {
    return this.customFieldsService.create(createCustomFieldDto);
  }

  @Get()
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get all custom fields with filtering' })
  @ApiResponse({ status: 200, description: 'Custom fields retrieved successfully' })
  @ApiQuery({ name: 'entityType', required: false, type: String })
  @ApiQuery({ name: 'type', required: false, type: String })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(
    @Query() query: CustomFieldQueryDto,
    @CurrentUser() user: any,
  ) {
    return this.customFieldsService.findAll(query, user);
  }

  @Get('entity/:entityType')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get custom fields for specific entity type' })
  @ApiResponse({ status: 200, description: 'Custom fields retrieved successfully' })
  async getByEntityType(
    @Param('entityType') entityType: string,
    @CurrentUser() user: any,
  ) {
    return this.customFieldsService.getByEntityType(entityType, user);
  }

  @Get('entity/:entityType/:entityId/values')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get custom field values for specific entity' })
  @ApiResponse({ status: 200, description: 'Custom field values retrieved successfully' })
  async getEntityValues(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseIntPipe) entityId: number,
    @CurrentUser() user: any,
  ) {
    return this.customFieldsService.getEntityValues(entityType, entityId, user);
  }

  @Get(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Get custom field by ID' })
  @ApiResponse({ status: 200, description: 'Custom field retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Custom field not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.customFieldsService.findOne(id);
  }

  @Patch(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Update custom field' })
  @ApiResponse({ status: 200, description: 'Custom field updated successfully' })
  @ApiResponse({ status: 404, description: 'Custom field not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCustomFieldDto: UpdateCustomFieldDto,
  ) {
    return this.customFieldsService.update(id, updateCustomFieldDto);
  }

  @Patch(':id/activate')
  @Auth('admin')
  @ApiOperation({ summary: 'Activate custom field' })
  @ApiResponse({ status: 200, description: 'Custom field activated successfully' })
  async activate(@Param('id', ParseIntPipe) id: number) {
    return this.customFieldsService.updateStatus(id, true);
  }

  @Patch(':id/deactivate')
  @Auth('admin')
  @ApiOperation({ summary: 'Deactivate custom field' })
  @ApiResponse({ status: 200, description: 'Custom field deactivated successfully' })
  async deactivate(@Param('id', ParseIntPipe) id: number) {
    return this.customFieldsService.updateStatus(id, false);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete custom field' })
  @ApiResponse({ status: 200, description: 'Custom field deleted successfully' })
  @ApiResponse({ status: 404, description: 'Custom field not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.customFieldsService.remove(id);
  }

  @Post('values')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Set custom field value' })
  @ApiResponse({ status: 200, description: 'Custom field value set successfully' })
  async setValue(
    @Body() setValueDto: SetCustomFieldValueDto,
    @CurrentUser() user: any,
  ) {
    return this.customFieldsService.setValue(setValueDto, user);
  }

  @Post('entity/:entityType/:entityId/values')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Set multiple custom field values for entity' })
  @ApiResponse({ status: 200, description: 'Custom field values set successfully' })
  async setEntityValues(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseIntPipe) entityId: number,
    @Body() values: { [fieldKey: string]: any },
    @CurrentUser() user: any,
  ) {
    return this.customFieldsService.setEntityValues(entityType, entityId, values, user);
  }

  @Delete('values/:entityType/:entityId/:fieldId')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Delete custom field value' })
  @ApiResponse({ status: 200, description: 'Custom field value deleted successfully' })
  async deleteValue(
    @Param('entityType') entityType: string,
    @Param('entityId', ParseIntPipe) entityId: number,
    @Param('fieldId', ParseIntPipe) fieldId: number,
    @CurrentUser() user: any,
  ) {
    return this.customFieldsService.deleteValue(entityType, entityId, fieldId, user);
  }

  @Get('types/available')
  @Auth('admin')
  @ApiOperation({ summary: 'Get available custom field types' })
  @ApiResponse({ status: 200, description: 'Field types retrieved successfully' })
  async getAvailableTypes() {
    return this.customFieldsService.getAvailableTypes();
  }

  @Get('entities/available')
  @Auth('admin')
  @ApiOperation({ summary: 'Get available entity types' })
  @ApiResponse({ status: 200, description: 'Entity types retrieved successfully' })
  async getAvailableEntities() {
    return this.customFieldsService.getAvailableEntities();
  }

  @Post(':id/duplicate')
  @Auth('admin')
  @ApiOperation({ summary: 'Duplicate custom field' })
  @ApiResponse({ status: 201, description: 'Custom field duplicated successfully' })
  async duplicate(@Param('id', ParseIntPipe) id: number) {
    return this.customFieldsService.duplicate(id);
  }
}
