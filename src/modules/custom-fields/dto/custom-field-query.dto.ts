import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { CustomFieldType, CustomFieldEntity } from '../entities/custom-field.entity';

export class CustomFieldQueryDto {
  @ApiProperty({
    description: 'Filter by entity type',
    enum: CustomFieldEntity,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldEntity)
  entityType?: CustomFieldEntity;

  @ApiProperty({
    description: 'Filter by field type',
    enum: CustomFieldType,
    required: false,
  })
  @IsOptional()
  @IsEnum(CustomFieldType)
  type?: CustomFieldType;

  @ApiProperty({
    description: 'Filter by active status',
    type: Boolean,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiProperty({
    description: 'Search in name, key, or description',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}
