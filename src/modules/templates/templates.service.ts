import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as Handlebars from 'handlebars';

import { Template, TemplateType, TemplateCategory } from './entities/template.entity';
import { CreateTemplateDto } from './dto/create-template.dto';
import { UpdateTemplateDto } from './dto/update-template.dto';
import { TemplateQueryDto } from './dto/template-query.dto';

@Injectable()
export class TemplatesService {
  constructor(
    @InjectRepository(Template)
    private readonly templateRepository: Repository<Template>,
  ) {
    this.registerHandlebarsHelpers();
  }

  private registerHandlebarsHelpers() {
    // Register custom Handlebars helpers
    Handlebars.registerHelper('formatDate', (date: Date, format: string) => {
      if (!date) return '';
      // Simple date formatting - in production, use a proper date library
      return new Date(date).toLocaleDateString();
    });

    Handlebars.registerHelper('formatCurrency', (amount: number) => {
      if (!amount) return '$0.00';
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
      }).format(amount);
    });

    Handlebars.registerHelper('uppercase', (str: string) => {
      return str ? str.toUpperCase() : '';
    });

    Handlebars.registerHelper('lowercase', (str: string) => {
      return str ? str.toLowerCase() : '';
    });
  }

  async create(createTemplateDto: CreateTemplateDto) {
    // Validate template syntax
    const validation = await this.validateTemplate(
      createTemplateDto.content,
      createTemplateDto.htmlContent,
    );

    if (!validation.isValid) {
      throw new BadRequestException(`Template validation failed: ${validation.errors.join(', ')}`);
    }

    const template = this.templateRepository.create(createTemplateDto);
    const savedTemplate = await this.templateRepository.save(template);

    return {
      template: savedTemplate,
      message: 'Template created successfully',
    };
  }

  async findAll(query: TemplateQueryDto) {
    const {
      page = 1,
      limit = 10,
      type,
      category,
      isActive,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    const queryBuilder = this.templateRepository.createQueryBuilder('template');

    // Apply filters
    if (type) {
      queryBuilder.andWhere('template.type = :type', { type });
    }

    if (category) {
      queryBuilder.andWhere('template.category = :category', { category });
    }

    if (isActive !== undefined) {
      queryBuilder.andWhere('template.isActive = :isActive', { isActive });
    }

    if (search) {
      queryBuilder.andWhere(
        '(template.name ILIKE :search OR template.subject ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`template.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    // Execute query
    const [templates, total] = await queryBuilder.getManyAndCount();

    return {
      templates,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const template = await this.templateRepository.findOne({ where: { id } });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    return template;
  }

  async findByCategory(category: string) {
    const templates = await this.templateRepository.find({
      where: { category: category as TemplateCategory, isActive: true },
      order: { name: 'ASC' },
    });

    return templates;
  }

  async update(id: number, updateTemplateDto: UpdateTemplateDto) {
    const template = await this.templateRepository.findOne({ where: { id } });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    // Validate template syntax if content is being updated
    if (updateTemplateDto.content || updateTemplateDto.htmlContent) {
      const validation = await this.validateTemplate(
        updateTemplateDto.content || template.content,
        updateTemplateDto.htmlContent || template.htmlContent,
      );

      if (!validation.isValid) {
        throw new BadRequestException(`Template validation failed: ${validation.errors.join(', ')}`);
      }
    }

    await this.templateRepository.update(id, updateTemplateDto);

    const updatedTemplate = await this.findOne(id);
    return {
      template: updatedTemplate,
      message: 'Template updated successfully',
    };
  }

  async updateStatus(id: number, isActive: boolean) {
    const template = await this.templateRepository.findOne({ where: { id } });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    await this.templateRepository.update(id, { isActive });

    return {
      message: `Template ${isActive ? 'activated' : 'deactivated'} successfully`,
    };
  }

  async remove(id: number) {
    const template = await this.templateRepository.findOne({ where: { id } });

    if (!template) {
      throw new NotFoundException('Template not found');
    }

    await this.templateRepository.remove(template);

    return {
      message: 'Template deleted successfully',
    };
  }

  async renderTemplate(id: number, variables: Record<string, any>) {
    const template = await this.findOne(id);

    try {
      // Compile and render text content
      const textTemplate = Handlebars.compile(template.content);
      const renderedContent = textTemplate(variables);

      let renderedHtmlContent = null;
      if (template.htmlContent) {
        const htmlTemplate = Handlebars.compile(template.htmlContent);
        renderedHtmlContent = htmlTemplate(variables);
      }

      // Render subject
      const subjectTemplate = Handlebars.compile(template.subject);
      const renderedSubject = subjectTemplate(variables);

      return {
        subject: renderedSubject,
        content: renderedContent,
        htmlContent: renderedHtmlContent,
        type: template.type,
        category: template.category,
      };
    } catch (error) {
      throw new BadRequestException(`Template rendering failed: ${error.message}`);
    }
  }

  async previewTemplate(id: number) {
    const template = await this.findOne(id);

    // Generate sample data based on template variables
    const sampleData = this.generateSampleData(template.variables || []);

    return this.renderTemplate(id, sampleData);
  }

  async duplicateTemplate(id: number) {
    const template = await this.findOne(id);

    const duplicatedTemplate = this.templateRepository.create({
      ...template,
      id: undefined,
      name: `${template.name} (Copy)`,
      isDefault: false,
    });

    const savedTemplate = await this.templateRepository.save(duplicatedTemplate);

    return {
      template: savedTemplate,
      message: 'Template duplicated successfully',
    };
  }

  async validateTemplate(content: string, htmlContent?: string) {
    const errors: string[] = [];

    try {
      // Validate text content
      Handlebars.compile(content);
    } catch (error) {
      errors.push(`Text content error: ${error.message}`);
    }

    if (htmlContent) {
      try {
        // Validate HTML content
        Handlebars.compile(htmlContent);
      } catch (error) {
        errors.push(`HTML content error: ${error.message}`);
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  async getCategories() {
    return Object.values(TemplateCategory).map((category) => ({
      value: category,
      label: category.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
    }));
  }

  async getAvailableVariables() {
    return [
      { name: 'clientName', description: 'Client full name', type: 'string' },
      { name: 'clientEmail', description: 'Client email address', type: 'string' },
      { name: 'orderNumber', description: 'Order number', type: 'string' },
      { name: 'propertyAddress', description: 'Property address', type: 'string' },
      { name: 'inspectionDate', description: 'Inspection date', type: 'date' },
      { name: 'inspectorName', description: 'Inspector name', type: 'string' },
      { name: 'inspectionFee', description: 'Inspection fee', type: 'currency' },
      { name: 'companyName', description: 'Company name', type: 'string' },
      { name: 'companyPhone', description: 'Company phone', type: 'string' },
      { name: 'companyEmail', description: 'Company email', type: 'string' },
    ];
  }

  private generateSampleData(variables: any[]): Record<string, any> {
    const sampleData: Record<string, any> = {
      clientName: 'John Doe',
      clientEmail: '<EMAIL>',
      orderNumber: 'ORD-2024-001',
      propertyAddress: '123 Main St, New York, NY 10001',
      inspectionDate: new Date(),
      inspectorName: 'Jane Smith',
      inspectionFee: 500.00,
      companyName: 'ABC Inspection Services',
      companyPhone: '(*************',
      companyEmail: '<EMAIL>',
    };

    // Add any custom variables with default values
    variables.forEach((variable) => {
      if (!sampleData[variable.name]) {
        sampleData[variable.name] = variable.defaultValue || 'Sample Value';
      }
    });

    return sampleData;
  }
}
