import { ApiProperty } from '@nestjs/swagger';
import {
  IsNumber,
  IsString,
  IsOptional,
  IsBoolean,
  IsObject,
  IsNotEmpty,
  Matches,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class RecurringPatternDto {
  @ApiProperty({
    description: 'Recurrence frequency',
    enum: ['daily', 'weekly', 'monthly'],
    example: 'weekly',
    required: false,
  })
  @IsString()
  @IsOptional()
  frequency?: 'daily' | 'weekly' | 'monthly';

  @ApiProperty({
    description: 'Recurrence interval',
    example: 1,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  interval?: number;

  @ApiProperty({
    description: 'Days of week (0=Sunday, 6=Saturday)',
    type: [Number],
    example: [1, 2, 3, 4, 5],
    required: false,
  })
  @IsNumber({}, { each: true })
  @IsOptional()
  daysOfWeek?: number[];

  @ApiProperty({
    description: 'End date for recurrence',
    example: '2024-12-31',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'End date must be in YYYY-MM-DD format' })
  endDate?: string;
}

export class CreateScheduleDto {
  @ApiProperty({
    description: 'Inspector ID',
    example: 1,
  })
  @IsNumber()
  inspectorId: number;

  @ApiProperty({
    description: 'Schedule date',
    example: '2024-01-15',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}-\d{2}-\d{2}$/, { message: 'Date must be in YYYY-MM-DD format' })
  date: string;

  @ApiProperty({
    description: 'Start time',
    example: '09:00',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'Start time must be in HH:MM format' })
  startTime: string;

  @ApiProperty({
    description: 'End time',
    example: '17:00',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, { message: 'End time must be in HH:MM format' })
  endTime: string;

  @ApiProperty({
    description: 'Is schedule available for assignment',
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  available?: boolean = true;

  @ApiProperty({
    description: 'Inspection order ID (if assigned)',
    example: 123,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  inspectionOrderId?: number;

  @ApiProperty({
    description: 'Schedule notes',
    example: 'Available for emergency calls',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Is recurring schedule',
    default: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isRecurring?: boolean = false;

  @ApiProperty({
    description: 'Recurring pattern configuration',
    type: RecurringPatternDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => RecurringPatternDto)
  @IsOptional()
  recurringPattern?: RecurringPatternDto;
}
