import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Inspector } from '../../inspectors/entities/inspector.entity';
import { Order } from '../../orders/entities/order.entity';

@Entity('schedules')
@Index(['inspectorId', 'date', 'startTime', 'endTime'])
export class Schedule {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  inspectorId: number;

  @Column({ type: 'date' })
  date: string;

  @Column({ type: 'time' })
  startTime: string;

  @Column({ type: 'time' })
  endTime: string;

  @Column({ default: true })
  available: boolean;

  @Column({ nullable: true })
  inspectionOrderId: number;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @Column({ default: false })
  isRecurring: boolean;

  @Column({ type: 'jsonb', nullable: true })
  recurringPattern: {
    frequency?: 'daily' | 'weekly' | 'monthly';
    interval?: number;
    daysOfWeek?: number[];
    endDate?: string;
  };

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Inspector, (inspector) => inspector.schedules)
  @JoinColumn({ name: 'inspectorId' })
  inspector: Inspector;

  @ManyToOne(() => Order, (order) => order.schedules, { nullable: true })
  @JoinColumn({ name: 'inspectionOrderId' })
  order: Order;
}
