import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { CronjobsService } from './cronjobs.service';
import { CreateCronjobDto } from './dto/create-cronjob.dto';
import { UpdateCronjobDto } from './dto/update-cronjob.dto';
import { CronjobQueryDto } from './dto/cronjob-query.dto';
import { Auth } from '../../common/decorators/auth.decorator';

@ApiTags('Cronjobs')
@Controller('cronjobs')
export class CronjobsController {
  constructor(private readonly cronjobsService: CronjobsService) {}

  @Post()
  @Auth('admin')
  @ApiOperation({ summary: 'Create a new cronjob' })
  @ApiResponse({ status: 201, description: 'Cronjob successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createCronjobDto: CreateCronjobDto) {
    return this.cronjobsService.create(createCronjobDto);
  }

  @Get()
  @Auth('admin')
  @ApiOperation({ summary: 'Get all cronjobs with filtering' })
  @ApiResponse({ status: 200, description: 'Cronjobs retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'isActive', required: false, type: Boolean })
  @ApiQuery({ name: 'jobType', required: false, type: String })
  async findAll(@Query() query: CronjobQueryDto) {
    return this.cronjobsService.findAll(query);
  }

  @Get('status')
  @Auth('admin')
  @ApiOperation({ summary: 'Get cronjob system status' })
  @ApiResponse({ status: 200, description: 'System status retrieved successfully' })
  async getSystemStatus() {
    return this.cronjobsService.getSystemStatus();
  }

  @Get('logs')
  @Auth('admin')
  @ApiOperation({ summary: 'Get cronjob execution logs' })
  @ApiResponse({ status: 200, description: 'Logs retrieved successfully' })
  @ApiQuery({ name: 'jobId', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getLogs(
    @Query('jobId') jobId?: number,
    @Query('limit') limit?: number,
  ) {
    return this.cronjobsService.getLogs(jobId, limit);
  }

  @Get(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Get cronjob by ID' })
  @ApiResponse({ status: 200, description: 'Cronjob retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Cronjob not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.cronjobsService.findOne(id);
  }

  @Patch(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Update cronjob' })
  @ApiResponse({ status: 200, description: 'Cronjob updated successfully' })
  @ApiResponse({ status: 404, description: 'Cronjob not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateCronjobDto: UpdateCronjobDto,
  ) {
    return this.cronjobsService.update(id, updateCronjobDto);
  }

  @Patch(':id/enable')
  @Auth('admin')
  @ApiOperation({ summary: 'Enable cronjob' })
  @ApiResponse({ status: 200, description: 'Cronjob enabled successfully' })
  async enable(@Param('id', ParseIntPipe) id: number) {
    return this.cronjobsService.updateStatus(id, true);
  }

  @Patch(':id/disable')
  @Auth('admin')
  @ApiOperation({ summary: 'Disable cronjob' })
  @ApiResponse({ status: 200, description: 'Cronjob disabled successfully' })
  async disable(@Param('id', ParseIntPipe) id: number) {
    return this.cronjobsService.updateStatus(id, false);
  }

  @Post(':id/run')
  @Auth('admin')
  @ApiOperation({ summary: 'Manually run cronjob' })
  @ApiResponse({ status: 200, description: 'Cronjob executed successfully' })
  async runJob(@Param('id', ParseIntPipe) id: number) {
    return this.cronjobsService.runJob(id);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete cronjob' })
  @ApiResponse({ status: 200, description: 'Cronjob deleted successfully' })
  @ApiResponse({ status: 404, description: 'Cronjob not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.cronjobsService.remove(id);
  }

  @Get(':id/history')
  @Auth('admin')
  @ApiOperation({ summary: 'Get cronjob execution history' })
  @ApiResponse({ status: 200, description: 'Execution history retrieved successfully' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  async getExecutionHistory(
    @Param('id', ParseIntPipe) id: number,
    @Query('limit') limit?: number,
  ) {
    return this.cronjobsService.getExecutionHistory(id, limit);
  }

  @Post('cleanup')
  @Auth('admin')
  @ApiOperation({ summary: 'Cleanup old cronjob logs' })
  @ApiResponse({ status: 200, description: 'Cleanup completed successfully' })
  async cleanupLogs(@Body() cleanupData: { olderThanDays: number }) {
    return this.cronjobsService.cleanupLogs(cleanupData.olderThanDays);
  }

  @Get('types/available')
  @Auth('admin')
  @ApiOperation({ summary: 'Get available job types' })
  @ApiResponse({ status: 200, description: 'Job types retrieved successfully' })
  async getAvailableJobTypes() {
    return this.cronjobsService.getAvailableJobTypes();
  }
}
