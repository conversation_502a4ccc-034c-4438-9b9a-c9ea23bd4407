import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { SchedulerRegistry } from '@nestjs/schedule';
import { <PERSON>ronJob } from 'cron';

import { Cronjob, JobType, JobStatus } from './entities/cronjob.entity';
import { CreateCronjobDto } from './dto/create-cronjob.dto';
import { UpdateCronjobDto } from './dto/update-cronjob.dto';
import { CronjobQueryDto } from './dto/cronjob-query.dto';
import { TaskSchedulerService } from './task-scheduler.service';

@Injectable()
export class CronjobsService {
  private readonly logger = new Logger(CronjobsService.name);

  constructor(
    @InjectRepository(Cronjob)
    private readonly cronjobRepository: Repository<Cronjob>,
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly taskSchedulerService: TaskSchedulerService,
  ) {
    this.initializeActiveCronjobs();
  }

  async create(createCronjobDto: CreateCronjobDto) {
    const cronjob = this.cronjobRepository.create(createCronjobDto);
    const savedCronjob = await this.cronjobRepository.save(cronjob);

    // Schedule the job if it's active
    if (savedCronjob.isActive) {
      await this.scheduleJob(savedCronjob);
    }

    return {
      cronjob: savedCronjob,
      message: 'Cronjob created successfully',
    };
  }

  async findAll(query: CronjobQueryDto) {
    const {
      page = 1,
      limit = 10,
      isActive,
      jobType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
    } = query;

    const queryBuilder = this.cronjobRepository.createQueryBuilder('cronjob');

    // Apply filters
    if (isActive !== undefined) {
      queryBuilder.andWhere('cronjob.isActive = :isActive', { isActive });
    }

    if (jobType) {
      queryBuilder.andWhere('cronjob.jobType = :jobType', { jobType });
    }

    if (search) {
      queryBuilder.andWhere(
        '(cronjob.name ILIKE :search OR cronjob.description ILIKE :search)',
        { search: `%${search}%` },
      );
    }

    // Apply sorting
    queryBuilder.orderBy(`cronjob.${sortBy}`, sortOrder);

    // Apply pagination
    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [cronjobs, total] = await queryBuilder.getManyAndCount();

    return {
      cronjobs,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number) {
    const cronjob = await this.cronjobRepository.findOne({ where: { id } });

    if (!cronjob) {
      throw new NotFoundException('Cronjob not found');
    }

    return cronjob;
  }

  async update(id: number, updateCronjobDto: UpdateCronjobDto) {
    const cronjob = await this.findOne(id);

    // Update the cronjob
    await this.cronjobRepository.update(id, updateCronjobDto);

    // Reschedule if schedule or status changed
    if (updateCronjobDto.schedule || updateCronjobDto.isActive !== undefined) {
      await this.unscheduleJob(cronjob.name);
      
      const updatedCronjob = await this.findOne(id);
      if (updatedCronjob.isActive) {
        await this.scheduleJob(updatedCronjob);
      }
    }

    const updatedCronjob = await this.findOne(id);
    return {
      cronjob: updatedCronjob,
      message: 'Cronjob updated successfully',
    };
  }

  async updateStatus(id: number, isActive: boolean) {
    const cronjob = await this.findOne(id);

    await this.cronjobRepository.update(id, { isActive });

    if (isActive) {
      await this.scheduleJob({ ...cronjob, isActive });
    } else {
      await this.unscheduleJob(cronjob.name);
    }

    return {
      message: `Cronjob ${isActive ? 'enabled' : 'disabled'} successfully`,
    };
  }

  async remove(id: number) {
    const cronjob = await this.findOne(id);

    // Unschedule the job
    await this.unscheduleJob(cronjob.name);

    // Remove from database
    await this.cronjobRepository.remove(cronjob);

    return {
      message: 'Cronjob deleted successfully',
    };
  }

  async runJob(id: number) {
    const cronjob = await this.findOne(id);

    try {
      // Update status to running
      await this.cronjobRepository.update(id, {
        status: JobStatus.RUNNING,
        lastRunAt: new Date(),
      });

      // Execute the job
      const result = await this.taskSchedulerService.executeJob(cronjob);

      // Update status to completed
      await this.cronjobRepository.update(id, {
        status: JobStatus.COMPLETED,
        lastCompletedAt: new Date(),
        runCount: cronjob.runCount + 1,
      });

      this.logger.log(`Cronjob ${cronjob.name} executed successfully`);

      return {
        message: 'Cronjob executed successfully',
        result,
      };
    } catch (error) {
      // Update status to failed
      await this.cronjobRepository.update(id, {
        status: JobStatus.FAILED,
        lastError: error.message,
        failureCount: cronjob.failureCount + 1,
      });

      this.logger.error(`Cronjob ${cronjob.name} failed:`, error.stack);

      throw error;
    }
  }

  async getSystemStatus() {
    const totalJobs = await this.cronjobRepository.count();
    const activeJobs = await this.cronjobRepository.count({
      where: { isActive: true },
    });
    const runningJobs = await this.cronjobRepository.count({
      where: { status: JobStatus.RUNNING },
    });
    const failedJobs = await this.cronjobRepository.count({
      where: { status: JobStatus.FAILED },
    });

    const recentExecutions = await this.cronjobRepository.find({
      where: { lastRunAt: LessThan(new Date(Date.now() - 24 * 60 * 60 * 1000)) },
      order: { lastRunAt: 'DESC' },
      take: 10,
    });

    return {
      totalJobs,
      activeJobs,
      runningJobs,
      failedJobs,
      recentExecutions,
      systemHealth: failedJobs === 0 ? 'healthy' : 'degraded',
    };
  }

  async getLogs(jobId?: number, limit: number = 100) {
    // This would typically fetch from a separate logs table
    // For now, return execution history
    const queryBuilder = this.cronjobRepository.createQueryBuilder('cronjob');

    if (jobId) {
      queryBuilder.where('cronjob.id = :jobId', { jobId });
    }

    queryBuilder
      .select([
        'cronjob.id',
        'cronjob.name',
        'cronjob.lastRunAt',
        'cronjob.lastCompletedAt',
        'cronjob.status',
        'cronjob.lastError',
      ])
      .orderBy('cronjob.lastRunAt', 'DESC')
      .limit(limit);

    const logs = await queryBuilder.getMany();

    return {
      logs,
      total: logs.length,
    };
  }

  async getExecutionHistory(id: number, limit: number = 50) {
    const cronjob = await this.findOne(id);

    // This would typically fetch from an execution history table
    // For now, return basic execution info
    return {
      cronjobId: id,
      cronjobName: cronjob.name,
      totalRuns: cronjob.runCount,
      totalFailures: cronjob.failureCount,
      lastRun: cronjob.lastRunAt,
      lastCompleted: cronjob.lastCompletedAt,
      lastError: cronjob.lastError,
      successRate: cronjob.runCount > 0 
        ? ((cronjob.runCount - cronjob.failureCount) / cronjob.runCount * 100).toFixed(2) + '%'
        : 'N/A',
    };
  }

  async cleanupLogs(olderThanDays: number) {
    const cutoffDate = new Date(Date.now() - olderThanDays * 24 * 60 * 60 * 1000);

    // This would typically clean up a separate logs table
    // For now, just reset error messages for old jobs
    const result = await this.cronjobRepository.update(
      { lastRunAt: LessThan(cutoffDate) },
      { lastError: null },
    );

    return {
      message: `Cleaned up logs older than ${olderThanDays} days`,
      affectedRows: result.affected,
    };
  }

  async getAvailableJobTypes() {
    return Object.values(JobType).map(type => ({
      value: type,
      label: type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    }));
  }

  private async initializeActiveCronjobs() {
    const activeCronjobs = await this.cronjobRepository.find({
      where: { isActive: true },
    });

    for (const cronjob of activeCronjobs) {
      await this.scheduleJob(cronjob);
    }

    this.logger.log(`Initialized ${activeCronjobs.length} active cronjobs`);
  }

  private async scheduleJob(cronjob: Cronjob) {
    try {
      const job = new CronJob(cronjob.schedule, async () => {
        await this.runJob(cronjob.id);
      });

      this.schedulerRegistry.addCronJob(cronjob.name, job);
      job.start();

      this.logger.log(`Scheduled cronjob: ${cronjob.name} with schedule: ${cronjob.schedule}`);
    } catch (error) {
      this.logger.error(`Failed to schedule cronjob ${cronjob.name}:`, error.stack);
    }
  }

  private async unscheduleJob(jobName: string) {
    try {
      if (this.schedulerRegistry.doesExist('cron', jobName)) {
        this.schedulerRegistry.deleteCronJob(jobName);
        this.logger.log(`Unscheduled cronjob: ${jobName}`);
      }
    } catch (error) {
      this.logger.error(`Failed to unschedule cronjob ${jobName}:`, error.stack);
    }
  }
}
