import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { CronjobsController } from './cronjobs.controller';
import { CronjobsService } from './cronjobs.service';
import { TaskSchedulerService } from './task-scheduler.service';
import { Cronjob } from './entities/cronjob.entity';
import { Order } from '../orders/entities/order.entity';
import { Schedule } from '../schedules/entities/schedule.entity';
import { EmailModule } from '../email/email.module';
import { OrdersModule } from '../orders/orders.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Cronjob, Order, Schedule]),
    BullModule.registerQueue({
      name: 'cronjobs',
    }),
    EmailModule,
    OrdersModule,
  ],
  controllers: [CronjobsController],
  providers: [CronjobsService, TaskSchedulerService],
  exports: [CronjobsService, TaskSchedulerService],
})
export class CronjobsModule {}
