import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { SettingsService } from './settings.service';
import { CreateSettingDto } from './dto/create-setting.dto';
import { UpdateSettingDto } from './dto/update-setting.dto';
import { UpdateSettingValueDto } from './dto/update-setting-value.dto';
import { SettingQueryDto } from './dto/setting-query.dto';
import { Auth } from '../../common/decorators/auth.decorator';

@ApiTags('Settings')
@Controller('settings')
export class SettingsController {
  constructor(private readonly settingsService: SettingsService) {}

  @Post()
  @Auth('admin')
  @ApiOperation({ summary: 'Create a new setting' })
  @ApiResponse({ status: 201, description: 'Setting successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createSettingDto: CreateSettingDto) {
    return this.settingsService.create(createSettingDto);
  }

  @Get()
  @Auth('admin')
  @ApiOperation({ summary: 'Get all settings with filtering' })
  @ApiResponse({ status: 200, description: 'Settings retrieved successfully' })
  @ApiQuery({ name: 'category', required: false, type: String })
  @ApiQuery({ name: 'group', required: false, type: String })
  @ApiQuery({ name: 'isVisible', required: false, type: Boolean })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(@Query() query: SettingQueryDto) {
    return this.settingsService.findAll(query);
  }

  @Get('public')
  @ApiOperation({ summary: 'Get public settings (no authentication required)' })
  @ApiResponse({ status: 200, description: 'Public settings retrieved successfully' })
  async getPublicSettings() {
    return this.settingsService.getPublicSettings();
  }

  @Get('categories')
  @Auth('admin')
  @ApiOperation({ summary: 'Get all setting categories' })
  @ApiResponse({ status: 200, description: 'Categories retrieved successfully' })
  async getCategories() {
    return this.settingsService.getCategories();
  }

  @Get('category/:category')
  @Auth('admin')
  @ApiOperation({ summary: 'Get settings by category' })
  @ApiResponse({ status: 200, description: 'Settings retrieved successfully' })
  async getByCategory(@Param('category') category: string) {
    return this.settingsService.getByCategory(category);
  }

  @Get('key/:key')
  @Auth('admin')
  @ApiOperation({ summary: 'Get setting by key' })
  @ApiResponse({ status: 200, description: 'Setting retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  async getByKey(@Param('key') key: string) {
    return this.settingsService.getByKey(key);
  }

  @Get('value/:key')
  @Auth('admin')
  @ApiOperation({ summary: 'Get setting value by key' })
  @ApiResponse({ status: 200, description: 'Setting value retrieved successfully' })
  async getValue(@Param('key') key: string) {
    return this.settingsService.getValue(key);
  }

  @Get(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Get setting by ID' })
  @ApiResponse({ status: 200, description: 'Setting retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.settingsService.findOne(id);
  }

  @Patch(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Update setting' })
  @ApiResponse({ status: 200, description: 'Setting updated successfully' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSettingDto: UpdateSettingDto,
  ) {
    return this.settingsService.update(id, updateSettingDto);
  }

  @Patch('key/:key/value')
  @Auth('admin')
  @ApiOperation({ summary: 'Update setting value by key' })
  @ApiResponse({ status: 200, description: 'Setting value updated successfully' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  async updateValue(
    @Param('key') key: string,
    @Body() updateValueDto: UpdateSettingValueDto,
  ) {
    return this.settingsService.updateValue(key, updateValueDto.value);
  }

  @Post('bulk-update')
  @Auth('admin')
  @ApiOperation({ summary: 'Bulk update multiple settings' })
  @ApiResponse({ status: 200, description: 'Settings updated successfully' })
  async bulkUpdate(@Body() settings: { key: string; value: any }[]) {
    return this.settingsService.bulkUpdate(settings);
  }

  @Post('reset/:key')
  @Auth('admin')
  @ApiOperation({ summary: 'Reset setting to default value' })
  @ApiResponse({ status: 200, description: 'Setting reset successfully' })
  async resetToDefault(@Param('key') key: string) {
    return this.settingsService.resetToDefault(key);
  }

  @Post('backup')
  @Auth('admin')
  @ApiOperation({ summary: 'Create settings backup' })
  @ApiResponse({ status: 200, description: 'Settings backup created' })
  async createBackup() {
    return this.settingsService.createBackup();
  }

  @Post('restore')
  @Auth('admin')
  @ApiOperation({ summary: 'Restore settings from backup' })
  @ApiResponse({ status: 200, description: 'Settings restored successfully' })
  async restoreBackup(@Body() backupData: any) {
    return this.settingsService.restoreBackup(backupData);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete setting' })
  @ApiResponse({ status: 200, description: 'Setting deleted successfully' })
  @ApiResponse({ status: 404, description: 'Setting not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.settingsService.remove(id);
  }

  @Post('validate')
  @Auth('admin')
  @ApiOperation({ summary: 'Validate setting value' })
  @ApiResponse({ status: 200, description: 'Validation result' })
  async validateSetting(@Body() data: { key: string; value: any }) {
    return this.settingsService.validateSetting(data.key, data.value);
  }
}
