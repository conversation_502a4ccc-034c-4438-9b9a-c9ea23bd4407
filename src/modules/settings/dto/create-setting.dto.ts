import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { SettingType, SettingCategory } from '../entities/setting.entity';

class SettingOptionsDto {
  @ApiProperty({ description: 'Minimum value for number type', required: false })
  @IsNumber()
  @IsOptional()
  min?: number;

  @ApiProperty({ description: 'Maximum value for number type', required: false })
  @IsNumber()
  @IsOptional()
  max?: number;

  @ApiProperty({
    description: 'Available choices for select type',
    type: [Object],
    required: false,
  })
  @IsOptional()
  choices?: { value: string; label: string }[];

  @ApiProperty({ description: 'Placeholder text', required: false })
  @IsString()
  @IsOptional()
  placeholder?: string;

  @ApiProperty({ description: 'Help text', required: false })
  @IsString()
  @IsOptional()
  helpText?: string;

  @ApiProperty({ description: 'Validation regex pattern', required: false })
  @IsString()
  @IsOptional()
  validation?: string;

  @ApiProperty({ description: 'Should value be encrypted', default: false })
  @IsBoolean()
  @IsOptional()
  encrypted?: boolean;
}

export class CreateSettingDto {
  @ApiProperty({
    description: 'Setting unique key',
    example: 'company_name',
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'Setting display name',
    example: 'Company Name',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Setting description',
    example: 'The name of your company',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Setting type',
    enum: SettingType,
    example: SettingType.STRING,
  })
  @IsEnum(SettingType)
  type: SettingType;

  @ApiProperty({
    description: 'Setting category',
    enum: SettingCategory,
    example: SettingCategory.GENERAL,
  })
  @IsEnum(SettingCategory)
  category: SettingCategory;

  @ApiProperty({
    description: 'Setting value',
    example: 'ABC Inspection Services',
    required: false,
  })
  @IsString()
  @IsOptional()
  value?: string;

  @ApiProperty({
    description: 'Default value',
    example: 'My Company',
    required: false,
  })
  @IsString()
  @IsOptional()
  defaultValue?: string;

  @ApiProperty({
    description: 'Setting options',
    type: SettingOptionsDto,
    required: false,
  })
  @IsObject()
  @ValidateNested()
  @Type(() => SettingOptionsDto)
  @IsOptional()
  options?: SettingOptionsDto;

  @ApiProperty({
    description: 'Is setting required',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isRequired?: boolean = false;

  @ApiProperty({
    description: 'Is setting secret (encrypted)',
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  isSecret?: boolean = false;

  @ApiProperty({
    description: 'Is setting editable',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isEditable?: boolean = true;

  @ApiProperty({
    description: 'Is setting visible in UI',
    default: true,
  })
  @IsBoolean()
  @IsOptional()
  isVisible?: boolean = true;

  @ApiProperty({
    description: 'Sort order',
    default: 0,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  sortOrder?: number = 0;

  @ApiProperty({
    description: 'Setting group',
    example: 'Company Information',
    required: false,
  })
  @IsString()
  @IsOptional()
  group?: string;
}
