import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum, IsBoolean } from 'class-validator';
import { Transform } from 'class-transformer';
import { SettingCategory } from '../entities/setting.entity';

export class SettingQueryDto {
  @ApiProperty({
    description: 'Filter by setting category',
    enum: SettingCategory,
    required: false,
  })
  @IsOptional()
  @IsEnum(SettingCategory)
  category?: SettingCategory;

  @ApiProperty({
    description: 'Filter by setting group',
    required: false,
  })
  @IsOptional()
  @IsString()
  group?: string;

  @ApiProperty({
    description: 'Filter by visible status',
    type: Boolean,
    required: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isVisible?: boolean;

  @ApiProperty({
    description: 'Search in name, key, or description',
    required: false,
  })
  @IsOptional()
  @IsString()
  search?: string;
}
