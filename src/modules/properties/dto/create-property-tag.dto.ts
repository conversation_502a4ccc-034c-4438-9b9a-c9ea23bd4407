import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean, IsNotEmpty, Matches } from 'class-validator';

export class CreatePropertyTagDto {
  @ApiProperty({
    description: 'Tag name',
    example: 'Luxury',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Tag color (hex code)',
    example: '#FF5733',
    required: false,
  })
  @IsString()
  @IsOptional()
  @Matches(/^#[0-9A-F]{6}$/i, { message: 'Color must be a valid hex code' })
  color?: string;

  @ApiProperty({
    description: 'Tag description',
    example: 'Properties with luxury features',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Tag active status',
    default: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean = true;
}
