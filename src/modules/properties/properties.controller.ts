import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';

import { PropertiesService } from './properties.service';
import { CreatePropertyDto } from './dto/create-property.dto';
import { UpdatePropertyDto } from './dto/update-property.dto';
import { PropertyQueryDto } from './dto/property-query.dto';
import { CreatePropertyTagDto } from './dto/create-property-tag.dto';
import { Auth } from '../../common/decorators/auth.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';

@ApiTags('Properties')
@Controller('properties')
export class PropertiesController {
  constructor(private readonly propertiesService: PropertiesService) {}

  @Post()
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Create a new property' })
  @ApiResponse({ status: 201, description: 'Property successfully created' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(
    @Body() createPropertyDto: CreatePropertyDto,
    @CurrentUser() user: any,
  ) {
    return this.propertiesService.create(createPropertyDto);
  }

  @Get()
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get all properties with filtering' })
  @ApiResponse({ status: 200, description: 'Properties retrieved successfully' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'propertyType', required: false, type: String })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiQuery({ name: 'search', required: false, type: String })
  async findAll(@Query() query: PropertyQueryDto) {
    return this.propertiesService.findAll(query);
  }

  @Get('search')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Search properties by address' })
  @ApiResponse({ status: 200, description: 'Properties found' })
  @ApiQuery({ name: 'q', required: true, type: String })
  async search(@Query('q') searchTerm: string) {
    return this.propertiesService.searchByAddress(searchTerm);
  }

  @Get('nearby')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Find properties nearby a location' })
  @ApiResponse({ status: 200, description: 'Nearby properties found' })
  @ApiQuery({ name: 'lat', required: true, type: Number })
  @ApiQuery({ name: 'lng', required: true, type: Number })
  @ApiQuery({ name: 'radius', required: false, type: Number })
  async findNearby(
    @Query('lat') latitude: number,
    @Query('lng') longitude: number,
    @Query('radius') radius: number = 5,
  ) {
    return this.propertiesService.findNearby(latitude, longitude, radius);
  }

  @Get(':id')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get property by ID' })
  @ApiResponse({ status: 200, description: 'Property retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.propertiesService.findOne(id);
  }

  @Patch(':id')
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Update property' })
  @ApiResponse({ status: 200, description: 'Property updated successfully' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updatePropertyDto: UpdatePropertyDto,
  ) {
    return this.propertiesService.update(id, updatePropertyDto);
  }

  @Delete(':id')
  @Auth('admin')
  @ApiOperation({ summary: 'Delete property' })
  @ApiResponse({ status: 200, description: 'Property deleted successfully' })
  @ApiResponse({ status: 404, description: 'Property not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.propertiesService.remove(id);
  }

  @Get(':id/orders')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get property inspection orders' })
  @ApiResponse({ status: 200, description: 'Property orders retrieved' })
  async getPropertyOrders(@Param('id', ParseIntPipe) id: number) {
    return this.propertiesService.getPropertyOrders(id);
  }

  // Property Tags endpoints
  @Post('tags')
  @Auth('admin')
  @ApiOperation({ summary: 'Create property tag' })
  @ApiResponse({ status: 201, description: 'Tag created successfully' })
  async createTag(@Body() createTagDto: CreatePropertyTagDto) {
    return this.propertiesService.createTag(createTagDto);
  }

  @Get('tags')
  @Auth('admin', 'inspector', 'client')
  @ApiOperation({ summary: 'Get all property tags' })
  @ApiResponse({ status: 200, description: 'Tags retrieved successfully' })
  async getTags() {
    return this.propertiesService.getTags();
  }

  @Patch(':id/tags')
  @Auth('admin', 'client')
  @ApiOperation({ summary: 'Update property tags' })
  @ApiResponse({ status: 200, description: 'Property tags updated' })
  async updatePropertyTags(
    @Param('id', ParseIntPipe) id: number,
    @Body() tagIds: { tagIds: number[] },
  ) {
    return this.propertiesService.updatePropertyTags(id, tagIds.tagIds);
  }
}
