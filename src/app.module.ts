import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bull';
import { ThrottlerModule } from '@nestjs/throttler';

// Modules
import { AuthModule } from './modules/auth/auth.module';
import { UsersModule } from './modules/users/users.module';
import { OrdersModule } from './modules/orders/orders.module';
import { InspectorsModule } from './modules/inspectors/inspectors.module';
import { SchedulesModule } from './modules/schedules/schedules.module';
import { PropertiesModule } from './modules/properties/properties.module';
import { EmailModule } from './modules/email/email.module';
import { CronjobsModule } from './modules/cronjobs/cronjobs.module';
import { TemplatesModule } from './modules/templates/templates.module';
import { SettingsModule } from './modules/settings/settings.module';
import { CustomFieldsModule } from './modules/custom-fields/custom-fields.module';

// Configuration
import { databaseConfig } from './config/database.config';
import { jwtConfig } from './config/jwt.config';
import { emailConfig } from './config/email.config';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),

    // Database
    TypeOrmModule.forRootAsync({
      useFactory: databaseConfig,
    }),

    // Task Scheduling
    ScheduleModule.forRoot(),

    // Queue Management
    BullModule.forRoot({
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT) || 6379,
      },
    }),

    // Rate Limiting
    ThrottlerModule.forRoot([
      {
        ttl: 60000, // 1 minute
        limit: 100, // 100 requests per minute
      },
    ]),

    // Feature Modules
    AuthModule,
    UsersModule,
    OrdersModule,
    InspectorsModule,
    SchedulesModule,
    PropertiesModule,
    EmailModule,
    CronjobsModule,
    TemplatesModule,
    SettingsModule,
    CustomFieldsModule,
  ],
})
export class AppModule {}
