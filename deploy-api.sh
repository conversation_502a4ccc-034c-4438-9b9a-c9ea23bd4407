#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

cd "$(dirname "$0")"

echo "Downloading Docker image: dominhtrung/inspection-order-mgmt:api"
docker pull dominhtrung/inspection-order-mgmt:api

echo "Starting services using docker-compose-api.yaml"
docker compose -f docker-compose-api.yaml  up -d

echo "Pruning unused Docker images"
docker image prune -f

echo "Deployment completed successfully!"
