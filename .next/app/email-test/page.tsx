"use client"

import type React from "react"

import { useState } from "react"

export default function EmailTestPage() {
  const [to, setTo] = useState("<EMAIL>")
  const [subject, setSubject] = useState("Test Email from Inspection System")
  const [html, setHtml] = useState(`
<h1>Test Email</h1>
<p>This is a test email sent from the Inspection System.</p>
<p>If you received this email, the email service is working correctly.</p>
<p>Time: ${new Date().toISOString()}</p>
  `)
  const [isSending, setIsSending] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const sendTestEmail = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSending(true)
    setResult(null)
    setError(null)

    try {
      const token = localStorage.getItem("accessToken")

      if (!token) {
        setError("You must be logged in to send a test email")
        setIsSending(false)
        return
      }

      const response = await fetch("/api/email/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          to,
          subject,
          html,
        }),
      })

      const data = await response.json()

      if (data.success) {
        setResult(data.data)
      } else {
        setError(data.error || "Failed to send test email")
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsSending(false)
    }
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Email Test Tool</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p>Email sent successfully!</p>
          <pre className="mt-2 text-sm">{JSON.stringify(result, null, 2)}</pre>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-md">
        <form onSubmit={sendTestEmail}>
          <div className="mb-4">
            <label className="block text-gray-700 mb-2">To:</label>
            <input
              type="email"
              value={to}
              onChange={(e) => setTo(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 mb-2">Subject:</label>
            <input
              type="text"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              className="w-full px-3 py-2 border rounded-md"
              required
            />
          </div>

          <div className="mb-4">
            <label className="block text-gray-700 mb-2">HTML Content:</label>
            <textarea
              value={html}
              onChange={(e) => setHtml(e.target.value)}
              className="w-full px-3 py-2 border rounded-md h-64 font-mono"
              required
            />
          </div>

          <button
            type="submit"
            className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50"
            disabled={isSending}
          >
            {isSending ? "Sending..." : "Send Test Email"}
          </button>
        </form>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Email Configuration</h2>
        <p className="mb-4">To test emails, you need to set up the following environment variables:</p>

        <div className="bg-gray-100 p-4 rounded-md">
          <pre className="text-sm">
            {`EMAIL_SMTP_HOST=smtp.example.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your-password
EMAIL_FROM_NAME="Inspection System"`}
          </pre>
        </div>

        <h3 className="text-lg font-semibold mt-6 mb-2">Recommended Test Email Services</h3>
        <ul className="list-disc pl-5 space-y-2">
          <li>
            <strong>Mailtrap</strong> - A safe testing environment that captures all emails without sending them to real
            recipients.
            <a href="https://mailtrap.io" target="_blank" rel="noopener noreferrer" className="text-blue-500 ml-2">
              Learn more
            </a>
          </li>
          <li>
            <strong>Ethereal Email</strong> - A fake SMTP service by Nodemailer that lets you view the emails that would
            have been sent.
            <a href="https://ethereal.email" target="_blank" rel="noopener noreferrer" className="text-blue-500 ml-2">
              Learn more
            </a>
          </li>
          <li>
            <strong>Gmail</strong> - You can use your Gmail account for testing, but you'll need to enable "Less secure
            app access" or use an App Password.
          </li>
        </ul>
      </div>
    </div>
  )
}
