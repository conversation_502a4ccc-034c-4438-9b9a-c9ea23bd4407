"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"

export default function CronJobsPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [cronJobs, setCronJobs] = useState<any[]>([])
  const [error, setError] = useState<string | null>(null)
  const [recipient, setRecipient] = useState("<EMAIL>")
  const [subject, setSubject] = useState("Test Email")
  const [content, setContent] = useState("<h1>Test Email</h1><p>This is a test email from the Inspection System.</p>")
  const [isCreating, setIsCreating] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const router = useRouter()

  useEffect(() => {
    fetchCronJobs()
  }, [])

  const fetchCronJobs = async () => {
    try {
      setIsLoading(true)
      const token = localStorage.getItem("accessToken")

      if (!token) {
        setError("You must be logged in to view this page")
        setIsLoading(false)
        return
      }

      const response = await fetch("/api/cronjobs", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Failed to fetch cronjobs")
      }

      setCronJobs(data.data.cronJobs || [])
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const createTestJob = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setIsCreating(true)
      const token = localStorage.getItem("accessToken")

      if (!token) {
        setError("You must be logged in to create a test job")
        return
      }

      const response = await fetch("/api/cronjobs/test", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          recipient,
          subject,
          content,
        }),
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Failed to create test job")
      }

      alert("Test job created successfully!")
      fetchCronJobs()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsCreating(false)
    }
  }

  const processJobs = async () => {
    try {
      setIsProcessing(true)
      const token = localStorage.getItem("accessToken")

      if (!token) {
        setError("You must be logged in to process jobs")
        return
      }

      const response = await fetch("/api/workers/cronjobs", {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Failed to process jobs")
      }

      alert(
        `Jobs processed: ${data.data.result.processed}, Success: ${data.data.result.success}, Failed: ${data.data.result.failed}`,
      )
      fetchCronJobs()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    } finally {
      setIsProcessing(false)
    }
  }

  const retryJob = async (id: number) => {
    try {
      const token = localStorage.getItem("accessToken")

      if (!token) {
        setError("You must be logged in to retry a job")
        return
      }

      const response = await fetch(`/api/cronjobs/${id}/retry`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Failed to retry job")
      }

      alert("Job queued for retry!")
      fetchCronJobs()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  const deleteJob = async (id: number) => {
    if (!confirm("Are you sure you want to delete this job?")) {
      return
    }

    try {
      const token = localStorage.getItem("accessToken")

      if (!token) {
        setError("You must be logged in to delete a job")
        return
      }

      const response = await fetch(`/api/cronjobs/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      })

      const data = await response.json()

      if (!data.success) {
        throw new Error(data.error || "Failed to delete job")
      }

      alert("Job deleted successfully!")
      fetchCronJobs()
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred")
    }
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">CronJobs Management</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{error}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Create Test Job</h2>
          <form onSubmit={createTestJob}>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Recipient Email</label>
              <input
                type="email"
                value={recipient}
                onChange={(e) => setRecipient(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Subject</label>
              <input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
                required
              />
            </div>
            <div className="mb-4">
              <label className="block text-gray-700 mb-2">Content (HTML)</label>
              <textarea
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="w-full px-3 py-2 border rounded-md h-32"
                required
              />
            </div>
            <button
              type="submit"
              className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 disabled:opacity-50"
              disabled={isCreating}
            >
              {isCreating ? "Creating..." : "Create Test Job"}
            </button>
          </form>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Process Jobs</h2>
          <p className="mb-4">
            Manually trigger the cronjob worker to process pending jobs. In a production environment, this would be
            handled by a scheduled task.
          </p>
          <button
            onClick={processJobs}
            className="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600 disabled:opacity-50"
            disabled={isProcessing}
          >
            {isProcessing ? "Processing..." : "Process Jobs Now"}
          </button>

          <div className="mt-6">
            <h3 className="font-semibold mb-2">Worker Setup Instructions</h3>
            <p className="text-sm text-gray-700 mb-2">
              To set up the worker to run automatically, you can use a cron job or a serverless function:
            </p>
            <pre className="bg-gray-100 p-3 rounded text-sm overflow-x-auto">
              # Run the worker every minute * * * * * cd /path/to/project && npm run worker
            </pre>
          </div>
        </div>
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-semibold mb-4">CronJobs List</h2>

        <div className="mb-4 flex justify-between items-center">
          <button onClick={fetchCronJobs} className="bg-gray-200 text-gray-800 px-3 py-1 rounded-md hover:bg-gray-300">
            Refresh
          </button>
        </div>

        {isLoading ? (
          <p>Loading...</p>
        ) : cronJobs.length === 0 ? (
          <p>No cronjobs found.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full bg-white border rounded-lg">
              <thead>
                <tr className="bg-gray-100">
                  <th className="py-2 px-4 border-b text-left">ID</th>
                  <th className="py-2 px-4 border-b text-left">Recipient</th>
                  <th className="py-2 px-4 border-b text-left">Type</th>
                  <th className="py-2 px-4 border-b text-left">Status</th>
                  <th className="py-2 px-4 border-b text-left">Created</th>
                  <th className="py-2 px-4 border-b text-left">Actions</th>
                </tr>
              </thead>
              <tbody>
                {cronJobs.map((job) => (
                  <tr key={job.id} className="hover:bg-gray-50">
                    <td className="py-2 px-4 border-b">{job.id}</td>
                    <td className="py-2 px-4 border-b">{job.recipient}</td>
                    <td className="py-2 px-4 border-b">{job.providerType}</td>
                    <td className="py-2 px-4 border-b">
                      <span
                        className={`px-2 py-1 rounded text-xs ${
                          job.status === "sent"
                            ? "bg-green-100 text-green-800"
                            : job.status === "pending"
                              ? "bg-yellow-100 text-yellow-800"
                              : job.status === "processing"
                                ? "bg-blue-100 text-blue-800"
                                : "bg-red-100 text-red-800"
                        }`}
                      >
                        {job.status}
                      </span>
                    </td>
                    <td className="py-2 px-4 border-b">{new Date(job.createdAt).toLocaleString()}</td>
                    <td className="py-2 px-4 border-b">
                      <div className="flex space-x-2">
                        {job.status === "failed" && (
                          <button
                            onClick={() => retryJob(job.id)}
                            className="bg-blue-500 text-white px-2 py-1 rounded text-xs"
                          >
                            Retry
                          </button>
                        )}
                        <button
                          onClick={() => deleteJob(job.id)}
                          className="bg-red-500 text-white px-2 py-1 rounded text-xs"
                        >
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  )
}
