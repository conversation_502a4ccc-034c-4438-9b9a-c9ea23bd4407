"use client"

import { useEffect, useState, useRef } from "react"
import { Loader2, AlertCircle } from "lucide-react"

export default function SwaggerPage() {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    // Load Swagger UI scripts and styles
    const loadSwaggerUI = async () => {
      try {
        console.log("Loading Swagger UI...")

        // Add Swagger UI CSS
        const linkEl = document.createElement("link")
        linkEl.rel = "stylesheet"
        linkEl.href = "https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui.css"
        document.head.appendChild(linkEl)

        // Load Swagger UI bundle
        const script = document.createElement("script")
        script.src = "https://unpkg.com/swagger-ui-dist@5.10.3/swagger-ui-bundle.js"
        script.async = true

        script.onload = async () => {
          try {
            console.log("Swagger UI script loaded, fetching spec...")

            // Fetch the OpenAPI spec
            const response = await fetch("/api/swagger.json")
            console.log("Fetch response:", response.status, response.statusText)

            if (!response.ok) {
              const errorText = await response.text()
              console.error("Failed to fetch OpenAPI spec:", errorText)
              setError(`Failed to fetch API documentation: ${response.status} ${response.statusText}`)
              setIsLoading(false)
              return
            }

            const spec = await response.json()
            console.log("Spec loaded successfully")

            // Initialize Swagger UI with additional configuration to fix selector issues
            if (containerRef.current && window.SwaggerUIBundle) {
              console.log("Initializing Swagger UI...")
              window.SwaggerUIBundle({
                spec,
                dom_id: "#swagger-ui",
                deepLinking: true,
                presets: [window.SwaggerUIBundle.presets.apis, window.SwaggerUIBundle.SwaggerUIStandalonePreset],
                layout: "BaseLayout",
                docExpansion: "list",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                displayOperationId: false, // Disable operation ID display
                displayRequestDuration: true,
                filter: true,
                syntaxHighlight: {
                  activate: true,
                  theme: "agate",
                },
                validatorUrl: null, // Disable validation
                withCredentials: true,
                requestInterceptor: (req: any) => {
                  // Add authorization header if token exists
                  const token = localStorage.getItem("accessToken")
                  if (token) {
                    req.headers.Authorization = `Bearer ${token}`
                  }
                  return req
                },
              })
              console.log("Swagger UI initialized")
            }
          } catch (err) {
            console.error("Error initializing Swagger UI:", err)
            setError(`Error initializing API documentation: ${err instanceof Error ? err.message : "Unknown error"}`)
          } finally {
            setIsLoading(false)
          }
        }

        script.onerror = (e) => {
          console.error("Failed to load Swagger UI scripts:", e)
          setError("Failed to load Swagger UI scripts")
          setIsLoading(false)
        }

        document.body.appendChild(script)

        return () => {
          document.head.removeChild(linkEl)
          document.body.removeChild(script)
        }
      } catch (err) {
        console.error("Error in Swagger UI setup:", err)
        setError("An error occurred while loading the API documentation")
        setIsLoading(false)
      }
    }

    loadSwaggerUI()
  }, [])

  return (
    <div className="min-h-screen flex flex-col">
      <div className="bg-gray-800 text-white p-4">
        <h1 className="text-2xl font-bold">Inspection API Documentation</h1>
      </div>

      <div className="flex-1">
        {isLoading && (
          <div className="flex items-center justify-center p-12">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
            <span className="ml-2 text-lg text-gray-700">Loading API documentation...</span>
          </div>
        )}

        {error && (
          <div className="p-8 text-center">
            <div className="flex justify-center mb-4">
              <AlertCircle className="h-12 w-12 text-red-500" />
            </div>
            <p className="text-red-500 text-lg font-medium">{error}</p>
            <p className="mt-2 text-gray-600">Please check the console for more details or try refreshing the page.</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        )}

        <div id="swagger-ui" ref={containerRef} className={isLoading || error ? "hidden" : ""}></div>
      </div>

      {/* Add custom CSS to fix selector issues */}
      <style jsx global>{`
        .swagger-ui .opblock-tag {
          font-size: 18px !important;
          margin: 10px 0 !important;
        }
        .swagger-ui .opblock {
          margin: 0 0 15px !important;
          border-radius: 4px !important;
        }
        .swagger-ui .opblock .opblock-summary {
          padding: 8px !important;
        }
        .swagger-ui .topbar {
          display: none !important;
        }
        .swagger-ui .info {
          margin: 20px 0 !important;
        }
        .swagger-ui .scheme-container {
          margin: 0 0 20px !important;
          padding: 15px !important;
        }
      `}</style>
    </div>
  )
}
