import Link from "next/link"
import NavLinks from "./components/nav-links"

export default function Home() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-100">
      <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
        <h1 className="text-3xl font-bold text-center mb-6">Inspection API</h1>
        <p className="text-gray-600 mb-8 text-center">
          A RESTful API for managing property inspections, inspectors, schedules, and more.
        </p>
        <div className="flex justify-center mb-6">
          <Link
            href="/swagger"
            className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
          >
            View API Documentation
          </Link>
        </div>
        <div className="flex justify-center">
          <NavLinks />
        </div>
      </div>
    </div>
  )
}
