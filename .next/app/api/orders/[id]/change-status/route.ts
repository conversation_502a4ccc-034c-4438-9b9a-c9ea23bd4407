import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { validateRequest, changeOrderStatusSchema } from "@/lib/validation"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// POST to change order status
export async function POST(request: Request, { params }: { params: { id: string } }) {
  return validateRequest(changeOrderStatusSchema)(request, async (req, validatedData) => {
    try {
      const auth = await authenticateRequest(request)

      if (!auth.authenticated) {
        return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
      }

      const id = Number.parseInt(params.id)

      if (isNaN(id)) {
        return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
      }

      // Check if inspection order exists
      const existingOrder = await sql`
        SELECT id, status, user_id FROM inspection_orders WHERE id = ${id}
      `

      if (existingOrder.length === 0) {
        return errorResponse("Inspection order not found", 404, "NOT_FOUND")
      }

      // Check if user has access to this order
      if (auth.user.role !== "admin" && existingOrder[0].user_id !== auth.user.userId) {
        return errorResponse("You don't have permission to update this order", 403, "FORBIDDEN")
      }

      // Check if status is already the same
      if (existingOrder[0].status === validatedData.status) {
        return successResponse(
          {
            message: `Order status is already ${validatedData.status}`,
            order: snakeToCamel(existingOrder[0]),
          },
          200,
        )
      }

      // Update the status
      const result = await sql`
        UPDATE inspection_orders
        SET 
          status = ${validatedData.status},
          updated_at = CURRENT_TIMESTAMP,
          updated_by = ${auth.user.userId}
        WHERE id = ${id}
        RETURNING *
      `

      // Create status history entry if needed
      if (validatedData.notes) {
        await sql`
          INSERT INTO order_status_history (
            order_id, previous_status, new_status, 
            changed_by, notes
          )
          VALUES (
            ${id}, 
            ${existingOrder[0].status}, 
            ${validatedData.status}, 
            ${auth.user.userId}, 
            ${validatedData.notes}
          )
        `
      }

      const updatedOrder = snakeToCamel(result[0])

      return successResponse(
        {
          message: `Order status changed from ${existingOrder[0].status} to ${validatedData.status}`,
          order: updatedOrder,
        },
        200,
      )
    } catch (error) {
      console.error("Database error:", error)
      return errorResponse("Failed to change order status", 500)
    }
  })
}
