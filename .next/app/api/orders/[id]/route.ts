import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { validateRequest, updateOrderSchema } from "@/lib/validation"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET a specific inspection order by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const result = await sql`
      SELECT io.*, 
             p.name as property_name,
             ARRAY(
               SELECT i.name 
               FROM inspectors i 
               WHERE i.id = ANY(io.assigned_inspector_ids)
             ) as inspector_names
      FROM inspection_orders io
      LEFT JOIN properties p ON io.property_id = p.id
      WHERE io.id = ${id}
    `

    if (result.length === 0) {
      return errorResponse("Inspection order not found", 404, "NOT_FOUND")
    }

    // Check if user has access to this order
    if (auth.user.role !== "admin" && result[0].user_id !== auth.user.userId) {
      return errorResponse("You don't have permission to access this order", 403, "FORBIDDEN")
    }

    const order = snakeToCamel(result[0])

    return successResponse({ order }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch inspection order", 500)
  }
}

// PUT update an inspection order
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  return validateRequest(updateOrderSchema)(request, async (req, validatedData) => {
    try {
      const auth = await authenticateRequest(request)

      if (!auth.authenticated) {
        return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
      }

      const id = Number.parseInt(params.id)

      if (isNaN(id)) {
        return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
      }

      // Check if inspection order exists and user has access
      const existingOrder = await sql`
        SELECT id, user_id FROM inspection_orders WHERE id = ${id}
      `

      if (existingOrder.length === 0) {
        return errorResponse("Inspection order not found", 404, "NOT_FOUND")
      }

      // Check if user has access to this order
      if (auth.user.role !== "admin" && existingOrder[0].user_id !== auth.user.userId) {
        return errorResponse("You don't have permission to update this order", 403, "FORBIDDEN")
      }

      // Calculate MD5 of property address if address is provided but MD5 is not
      let propertyMD5 = validatedData.propertyMD5
      if (validatedData.propertyAddress && !propertyMD5) {
        propertyMD5 = await generateMD5(validatedData.propertyAddress)
      }

      // Build the SET clause dynamically
      const updates = []
      const values: any = {}

      // Client information
      if (validatedData.clientName !== undefined) updates.push(sql`client_name = ${validatedData.clientName}`)
      if (validatedData.clientPhone !== undefined) updates.push(sql`client_phone = ${validatedData.clientPhone}`)
      if (validatedData.clientEmail !== undefined) updates.push(sql`client_email = ${validatedData.clientEmail}`)

      // Property information
      if (validatedData.propertyAddress !== undefined)
        updates.push(sql`property_address = ${validatedData.propertyAddress}`)
      if (propertyMD5 !== undefined) updates.push(sql`property_md5 = ${propertyMD5}`)
      if (validatedData.propertyType !== undefined) updates.push(sql`property_type = ${validatedData.propertyType}`)
      if (validatedData.yearBuilt !== undefined) updates.push(sql`year_built = ${validatedData.yearBuilt}`)
      if (validatedData.foundationType !== undefined)
        updates.push(sql`foundation_type = ${validatedData.foundationType}`)
      if (validatedData.gateCode !== undefined) updates.push(sql`gate_code = ${validatedData.gateCode}`)
      if (validatedData.lockboxCode !== undefined) updates.push(sql`lockbox_code = ${validatedData.lockboxCode}`)
      if (validatedData.mlsNumber !== undefined) updates.push(sql`mls_number = ${validatedData.mlsNumber}`)
      if (validatedData.propertyTags !== undefined) updates.push(sql`property_tags = ${validatedData.propertyTags}`)
      if (validatedData.addOns !== undefined) updates.push(sql`add_ons = ${JSON.stringify(validatedData.addOns)}`)

      // Agent information
      if (validatedData.agentName !== undefined) updates.push(sql`agent_name = ${validatedData.agentName}`)
      if (validatedData.agentEmail !== undefined) updates.push(sql`agent_email = ${validatedData.agentEmail}`)
      if (validatedData.agentPhone !== undefined) updates.push(sql`agent_phone = ${validatedData.agentPhone}`)
      if (validatedData.agentType !== undefined) updates.push(sql`agent_type = ${validatedData.agentType}`)

      // Order information
      if (validatedData.inspectionFees !== undefined)
        updates.push(sql`inspection_fees = ${validatedData.inspectionFees}`)
      if (validatedData.status !== undefined) updates.push(sql`status = ${validatedData.status}`)
      if (validatedData.inspectionDate !== undefined)
        updates.push(sql`inspection_date = ${validatedData.inspectionDate}`)
      if (validatedData.assignedInspectorIds !== undefined)
        updates.push(sql`assigned_inspector_ids = ${validatedData.assignedInspectorIds}`)

      // Legacy fields
      if (validatedData.propertyId !== undefined) updates.push(sql`property_id = ${validatedData.propertyId}`)

      // Always update these fields
      updates.push(sql`updated_at = CURRENT_TIMESTAMP`)
      updates.push(sql`updated_by = ${auth.user.userId}`)

      if (updates.length === 0) {
        return errorResponse("No fields to update", 400, "INVALID_INPUT")
      }

      // Update inspection order
      const result = await sql`
        UPDATE inspection_orders
        SET ${sql.join(updates, sql`, `)}
        WHERE id = ${id}
        RETURNING *
      `

      const updatedOrder = snakeToCamel(result[0])

      return successResponse({ order: updatedOrder }, 200)
    } catch (error) {
      console.error("Database error:", error)
      return errorResponse("Failed to update inspection order", 500)
    }
  })
}

// DELETE an inspection order
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if inspection order exists and user has access
    const existingOrder = await sql`
      SELECT id, user_id FROM inspection_orders WHERE id = ${id}
    `

    if (existingOrder.length === 0) {
      return errorResponse("Inspection order not found", 404, "NOT_FOUND")
    }

    // Only admin or the order creator can delete
    if (auth.user.role !== "admin" && existingOrder[0].user_id !== auth.user.userId) {
      return errorResponse("You don't have permission to delete this order", 403, "FORBIDDEN")
    }

    // Delete inspection order
    await sql`
      DELETE FROM inspection_orders WHERE id = ${id}
    `

    return successResponse({ message: `Inspection order ${id} deleted successfully` }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to delete inspection order", 500)
  }
}

// Helper function to generate MD5 hash
async function generateMD5(text: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(text)
  const hashBuffer = await crypto.subtle.digest("MD5", data)

  // Convert to hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("")
}
