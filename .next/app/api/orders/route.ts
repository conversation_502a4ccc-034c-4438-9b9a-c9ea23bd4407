import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { validateRequest, createOrderSchema } from "@/lib/validation"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all inspection orders
export async function GET(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Get query parameters for pagination, filtering, and sorting
    const { searchParams } = new URL(request.url)
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "10")
    const status = searchParams.get("status")
    const search = searchParams.get("search")
    const sortBy = searchParams.get("sortBy") || "created_at"
    const sortOrder = searchParams.get("sortOrder") || "desc"

    // Calculate offset
    const offset = (page - 1) * pageSize

    // Start building the query
    let countQuery = sql`SELECT COUNT(*) FROM inspection_orders`
    let dataQuery = sql`
      SELECT io.*, 
             p.name as property_name,
             ARRAY(
               SELECT i.name 
               FROM inspectors i 
               WHERE i.id = ANY(io.assigned_inspector_ids)
             ) as inspector_names
      FROM inspection_orders io
      LEFT JOIN properties p ON io.property_id = p.id
    `

    // Add WHERE clauses if filters are provided
    const whereConditions = []

    if (status) {
      whereConditions.push(sql`io.status = ${status}`)
    }

    if (search) {
      whereConditions.push(sql`(
        io.client_name ILIKE ${`%${search}%`} OR
        io.client_email ILIKE ${`%${search}%`} OR
        io.property_address ILIKE ${`%${search}%`} OR
        io.agent_name ILIKE ${`%${search}%`}
      )`)
    }

    // If user is not admin, only show their orders
    if (auth.user.role !== "admin") {
      whereConditions.push(sql`io.user_id = ${auth.user.userId}`)
    }

    // Combine WHERE conditions if any
    if (whereConditions.length > 0) {
      // Instead of using sql.join(), we'll build the WHERE clause manually
      if (whereConditions.length === 1) {
        countQuery = sql`${countQuery} WHERE ${whereConditions[0]}`
        dataQuery = sql`${dataQuery} WHERE ${whereConditions[0]}`
      } else if (whereConditions.length === 2) {
        countQuery = sql`${countQuery} WHERE ${whereConditions[0]} AND ${whereConditions[1]}`
        dataQuery = sql`${dataQuery} WHERE ${whereConditions[0]} AND ${whereConditions[1]}`
      } else if (whereConditions.length === 3) {
        countQuery = sql`${countQuery} WHERE ${whereConditions[0]} AND ${whereConditions[1]} AND ${whereConditions[2]}`
        dataQuery = sql`${dataQuery} WHERE ${whereConditions[0]} AND ${whereConditions[1]} AND ${whereConditions[2]}`
      }
    }

    // Add ORDER BY and LIMIT clauses
    // Instead of using sql.identifier, we'll use a switch statement to handle different sort columns
    let orderByClause
    switch (sortBy) {
      case "created_at":
        orderByClause = sortOrder === "asc" ? sql`io.created_at ASC` : sql`io.created_at DESC`
        break
      case "inspection_date":
        orderByClause = sortOrder === "asc" ? sql`io.inspection_date ASC` : sql`io.inspection_date DESC`
        break
      case "status":
        orderByClause = sortOrder === "asc" ? sql`io.status ASC` : sql`io.status DESC`
        break
      case "client_name":
        orderByClause = sortOrder === "asc" ? sql`io.client_name ASC` : sql`io.client_name DESC`
        break
      case "property_address":
        orderByClause = sortOrder === "asc" ? sql`io.property_address ASC` : sql`io.property_address DESC`
        break
      default:
        orderByClause = sql`io.created_at DESC`
    }

    dataQuery = sql`
      ${dataQuery}
      ORDER BY ${orderByClause}
      LIMIT ${pageSize} OFFSET ${offset}
    `

    // Execute the queries
    const [countResult, dataResult] = await Promise.all([sql`${countQuery}`, sql`${dataQuery}`])

    const total = Number.parseInt(countResult[0].count)
    const totalPages = Math.ceil(total / pageSize)

    // Convert snake_case to camelCase
    const orders = dataResult.map((order) => snakeToCamel(order))

    return successResponse(
      {
        orders,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
        },
      },
      200,
    )
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch inspection orders", 500)
  }
}

// POST create a new inspection order
export async function POST(request: Request) {
  return validateRequest(createOrderSchema)(request, async (req, validatedData) => {
    try {
      const auth = await authenticateRequest(request)

      if (!auth.authenticated) {
        return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
      }

      // Generate a unique inspection order ID if not provided
      const inspectionOrderId =
        validatedData.inspectionOrderId || `INS-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`

      // Calculate MD5 of property address if not provided
      const propertyMD5 = validatedData.propertyMD5 || (await generateMD5(validatedData.propertyAddress))

      // Insert new inspection order
      const result = await sql`
        INSERT INTO inspection_orders (
          inspection_order_id, property_id, 
          client_name, client_phone, client_email,
          property_address, property_md5, property_type,
          year_built, foundation_type, gate_code,
          lockbox_code, mls_number, property_tags,
          add_ons, agent_name, agent_email,
          agent_phone, agent_type, inspection_fees,
          status, inspection_date, user_id,
          created_by, assigned_inspector_ids
        )
        VALUES (
          ${inspectionOrderId}, 
          ${validatedData.propertyId || null}, 
          ${validatedData.clientName},
          ${validatedData.clientPhone || null},
          ${validatedData.clientEmail || null},
          ${validatedData.propertyAddress},
          ${propertyMD5},
          ${validatedData.propertyType || null},
          ${validatedData.yearBuilt || null},
          ${validatedData.foundationType || null},
          ${validatedData.gateCode || null},
          ${validatedData.lockboxCode || null},
          ${validatedData.mlsNumber || null},
          ${validatedData.propertyTags || null},
          ${validatedData.addOns ? JSON.stringify(validatedData.addOns) : null},
          ${validatedData.agentName || null},
          ${validatedData.agentEmail || null},
          ${validatedData.agentPhone || null},
          ${validatedData.agentType || null},
          ${validatedData.inspectionFees || null},
          ${validatedData.status || "pending"},
          ${validatedData.inspectionDate || null},
          ${validatedData.userId || auth.user.userId},
          ${auth.user.userId},
          ${validatedData.assignedInspectorIds || null}
        )
        RETURNING *
      `

      const newOrder = snakeToCamel(result[0])

      return successResponse({ order: newOrder }, 201)
    } catch (error) {
      console.error("Database error:", error)
      return errorResponse("Failed to create inspection order", 500)
    }
  })
}

// Helper function to generate MD5 hash
async function generateMD5(text: string): Promise<string> {
  const encoder = new TextEncoder()
  const data = encoder.encode(text)
  const hashBuffer = await crypto.subtle.digest("MD5", data)

  // Convert to hex string
  const hashArray = Array.from(new Uint8Array(hashBuffer))
  return hashArray.map((b) => b.toString(16).padStart(2, "0")).join("")
}
