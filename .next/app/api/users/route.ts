import { sql, snakeTo<PERSON>amel } from "@/lib/db"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { NextResponse } from "next/server"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all users (protected route - requires authentication and admin role)
export async function GET(request: Request) {
  // Authenticate the request
  const auth = await authenticateRequest(request)

  if (!auth.authenticated) {
    return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
  }

  // Check if user has admin role
  if (auth.user.role !== "admin") {
    return errorResponse("Admin access required", 403, "FORBIDDEN")
  }

  try {
    const result = await sql`
      SELECT id, name, email, role, created_at, updated_at
      FROM users
      ORDER BY id
    `

    // Convert snake_case to camelCase
    const users = result.map((user) => snakeToCamel(user))

    return successResponse({ users }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch users", 500)
  }
}

// POST create a new user
export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      return errorResponse("Name, email, and password are required", 400, "INVALID_INPUT")
    }

    // Check if email already exists
    const existingUser = await sql`
      SELECT id FROM users WHERE email = ${body.email}
    `

    if (existingUser.length > 0) {
      return errorResponse("Email already in use", 409, "CONFLICT")
    }

    // Insert new user
    const result = await sql`
      INSERT INTO users (name, email, password, role)
      VALUES (${body.name}, ${body.email}, ${body.password}, ${body.role || "user"})
      RETURNING id, name, email, role, created_at, updated_at
    `

    const newUser = snakeToCamel(result[0])

    return successResponse({ user: newUser }, 201)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to create user", 500)
  }
}
