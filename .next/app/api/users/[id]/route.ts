import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse } from "@/lib/api-response"
import { handleCorsOptions } from "@/lib/cors"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return handleCorsOptions()
}

// GET a specific user by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const result = await sql`
      SELECT id, name, email, role, created_at, updated_at
      FROM users
      WHERE id = ${id}
    `

    if (result.length === 0) {
      return errorResponse("User not found", 404, "NOT_FOUND")
    }

    const user = snakeToCamel(result[0])

    return successResponse({ user }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch user", 500)
  }
}

// PUT update a user
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const body = await request.json()

    // Check if user exists
    const existingUser = await sql`
      SELECT id FROM users WHERE id = ${id}
    `

    if (existingUser.length === 0) {
      return errorResponse("User not found", 404, "NOT_FOUND")
    }

    // Update user
    const result = await sql`
      UPDATE users
      SET 
        name = COALESCE(${body.name}, name),
        email = COALESCE(${body.email}, email),
        role = COALESCE(${body.role}, role),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, name, email, role, created_at, updated_at
    `

    const updatedUser = snakeToCamel(result[0])

    return successResponse({ user: updatedUser }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to update user", 500)
  }
}

// DELETE a user
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if user exists
    const existingUser = await sql`
      SELECT id FROM users WHERE id = ${id}
    `

    if (existingUser.length === 0) {
      return errorResponse("User not found", 404, "NOT_FOUND")
    }

    // Delete user
    await sql`
      DELETE FROM users WHERE id = ${id}
    `

    return successResponse({ message: `User ${id} deleted successfully` }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to delete user", 500)
  }
}
