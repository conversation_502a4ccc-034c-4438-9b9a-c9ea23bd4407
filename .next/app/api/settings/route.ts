import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all settings
export async function GET(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url)
    const type = searchParams.get("type")

    // Build the query
    let query = sql`
      SELECT id, name, type, value, created_at, created_by, updated_at, updated_by
      FROM settings
    `

    // Add WHERE clause if type filter is provided
    if (type) {
      query = sql`${query} WHERE type = ${type}`
    }

    // Add ORDER BY clause
    query = sql`${query} ORDER BY type, name`

    // Execute the query
    const result = await query

    // Convert snake_case to camelCase
    const settings = result.map((setting) => snakeToCamel(setting))

    return successResponse({ settings }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch settings", 500)
  }
}

// POST create a new setting
export async function POST(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Check if user has admin role
    if (auth.user.role !== "admin") {
      return errorResponse("Only administrators can create settings", 403, "FORBIDDEN")
    }

    const body = await request.json()

    // Validate required fields
    if (!body.name || !body.type || body.value === undefined) {
      return errorResponse("Name, type, and value are required", 400, "INVALID_INPUT")
    }

    // Check if name and type combination already exists
    const existingSetting = await sql`
      SELECT id FROM settings WHERE name = ${body.name} AND type = ${body.type}
    `

    if (existingSetting.length > 0) {
      return errorResponse("A setting with this name and type already exists", 409, "CONFLICT")
    }

    // Insert new setting
    const result = await sql`
      INSERT INTO settings (
        name, type, value, created_by
      )
      VALUES (
        ${body.name}, 
        ${body.type}, 
        ${body.value}, 
        ${auth.user.userId}
      )
      RETURNING id, name, type, value, created_at, created_by, updated_at, updated_by
    `

    const newSetting = snakeToCamel(result[0])

    return successResponse({ setting: newSetting }, 201)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to create setting", 500)
  }
}
