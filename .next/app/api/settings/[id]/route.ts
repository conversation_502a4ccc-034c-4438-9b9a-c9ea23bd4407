import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET a specific setting by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const result = await sql`
      SELECT id, name, type, value, created_at, created_by, updated_at, updated_by
      FROM settings
      WHERE id = ${id}
    `

    if (result.length === 0) {
      return errorResponse("Setting not found", 404, "NOT_FOUND")
    }

    const setting = snakeToCamel(result[0])

    return successResponse({ setting }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch setting", 500)
  }
}

// PUT update a setting
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Check if user has admin role
    if (auth.user.role !== "admin") {
      return errorResponse("Only administrators can update settings", 403, "FORBIDDEN")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const body = await request.json()

    // Check if setting exists
    const existingSetting = await sql`
      SELECT id FROM settings WHERE id = ${id}
    `

    if (existingSetting.length === 0) {
      return errorResponse("Setting not found", 404, "NOT_FOUND")
    }

    // If name and type are being updated, check if the combination already exists
    if (body.name && body.type) {
      const nameTypeCheck = await sql`
        SELECT id FROM settings 
        WHERE name = ${body.name} AND type = ${body.type} AND id != ${id}
      `

      if (nameTypeCheck.length > 0) {
        return errorResponse("A setting with this name and type already exists", 409, "CONFLICT")
      }
    }

    // Update setting
    const result = await sql`
      UPDATE settings
      SET 
        name = COALESCE(${body.name}, name),
        type = COALESCE(${body.type}, type),
        value = COALESCE(${body.value}, value),
        updated_at = CURRENT_TIMESTAMP,
        updated_by = ${auth.user.userId}
      WHERE id = ${id}
      RETURNING id, name, type, value, created_at, created_by, updated_at, updated_by
    `

    const updatedSetting = snakeToCamel(result[0])

    return successResponse({ setting: updatedSetting }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to update setting", 500)
  }
}

// DELETE a setting
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Check if user has admin role
    if (auth.user.role !== "admin") {
      return errorResponse("Only administrators can delete settings", 403, "FORBIDDEN")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if setting exists
    const existingSetting = await sql`
      SELECT id FROM settings WHERE id = ${id}
    `

    if (existingSetting.length === 0) {
      return errorResponse("Setting not found", 404, "NOT_FOUND")
    }

    // Delete setting
    await sql`
      DELETE FROM settings WHERE id = ${id}
    `

    return successResponse({ message: `Setting ${id} deleted successfully` }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to delete setting", 500)
  }
}
