import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET all schedules
export async function GET() {
  try {
    const result = await sql`
      SELECT s.id, s.inspector_id, s.date, s.start_time, s.end_time, 
             s.available, s.inspection_order_id, s.created_at, s.updated_at,
             i.name as inspector_name
      FROM schedules s
      LEFT JOIN inspectors i ON s.inspector_id = i.id
      ORDER BY s.date, s.start_time
    `

    // Convert snake_case to camelCase
    const schedules = result.map((schedule) => snakeToCamel(schedule))

    return NextResponse.json({ schedules }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch schedules" }, { status: 500 })
  }
}

// POST create a new schedule
export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.inspectorId || !body.date || !body.startTime || !body.endTime) {
      return NextResponse.json({ error: "Inspector ID, date, start time, and end time are required" }, { status: 400 })
    }

    // Insert new schedule
    const result = await sql`
      INSERT INTO schedules (
        inspector_id, date, start_time, end_time, 
        available, inspection_order_id
      )
      VALUES (
        ${body.inspectorId}, 
        ${body.date}, 
        ${body.startTime}, 
        ${body.endTime}, 
        ${body.available !== undefined ? body.available : true}, 
        ${body.inspectionOrderId || null}
      )
      RETURNING id, inspector_id, date, start_time, end_time, 
                available, inspection_order_id, created_at, updated_at
    `

    const newSchedule = snakeToCamel(result[0])

    return NextResponse.json({ schedule: newSchedule }, { status: 201 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to create schedule" }, { status: 500 })
  }
}
