import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET all schedules for a specific inspector
export async function GET(request: Request, { params }: { params: { inspectorId: string } }) {
  try {
    const inspectorId = Number.parseInt(params.inspectorId)

    if (isNaN(inspectorId)) {
      return NextResponse.json({ error: "Invalid inspector ID format" }, { status: 400 })
    }

    const result = await sql`
      SELECT s.id, s.inspector_id, s.date, s.start_time, s.end_time, 
             s.available, s.inspection_order_id, s.created_at, s.updated_at,
             i.name as inspector_name,
             io.inspection_order_id as order_number,
             p.name as property_name
      FROM schedules s
      LEFT JOIN inspectors i ON s.inspector_id = i.id
      LEFT JOIN inspection_orders io ON s.inspection_order_id = io.id
      LEFT JOIN properties p ON io.property_id = p.id
      WHERE s.inspector_id = ${inspectorId}
      ORDER BY s.date, s.start_time
    `

    // Convert snake_case to camelCase
    const schedules = result.map((schedule) => snakeToCamel(schedule))

    return NextResponse.json({ schedules }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch inspector schedules" }, { status: 500 })
  }
}
