import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET a specific schedule by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    const result = await sql`
      SELECT s.id, s.inspector_id, s.date, s.start_time, s.end_time, 
             s.available, s.inspection_order_id, s.created_at, s.updated_at,
             i.name as inspector_name
      FROM schedules s
      LEFT JOIN inspectors i ON s.inspector_id = i.id
      WHERE s.id = ${id}
    `

    if (result.length === 0) {
      return NextResponse.json({ error: "Schedule not found" }, { status: 404 })
    }

    const schedule = snakeToCamel(result[0])

    return NextResponse.json({ schedule }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch schedule" }, { status: 500 })
  }
}

// PUT update a schedule
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    const body = await request.json()

    // Check if schedule exists
    const existingSchedule = await sql`
      SELECT id FROM schedules WHERE id = ${id}
    `

    if (existingSchedule.length === 0) {
      return NextResponse.json({ error: "Schedule not found" }, { status: 404 })
    }

    // Update schedule
    const result = await sql`
      UPDATE schedules
      SET 
        inspector_id = COALESCE(${body.inspectorId}, inspector_id),
        date = COALESCE(${body.date}, date),
        start_time = COALESCE(${body.startTime}, start_time),
        end_time = COALESCE(${body.endTime}, end_time),
        available = COALESCE(${body.available}, available),
        inspection_order_id = COALESCE(${body.inspectionOrderId}, inspection_order_id),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, inspector_id, date, start_time, end_time, 
                available, inspection_order_id, created_at, updated_at
    `

    const updatedSchedule = snakeToCamel(result[0])

    return NextResponse.json({ schedule: updatedSchedule }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to update schedule" }, { status: 500 })
  }
}

// DELETE a schedule
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    // Check if schedule exists
    const existingSchedule = await sql`
      SELECT id FROM schedules WHERE id = ${id}
    `

    if (existingSchedule.length === 0) {
      return NextResponse.json({ error: "Schedule not found" }, { status: 404 })
    }

    // Delete schedule
    await sql`
      DELETE FROM schedules WHERE id = ${id}
    `

    return NextResponse.json({ message: `Schedule ${id} deleted successfully` }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to delete schedule" }, { status: 500 })
  }
}
