import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import crypto from "crypto"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all cronjobs
export async function GET(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status")
    const providerType = searchParams.get("providerType")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "20")
    const offset = (page - 1) * pageSize

    // Build the query
    let countQuery = sql`SELECT COUNT(*) FROM cronjobs`
    let dataQuery = sql`
      SELECT * FROM cronjobs
    `

    // Add WHERE clauses if filters are provided
    const whereConditions = []

    if (status) {
      whereConditions.push(sql`status = ${status}`)
    }

    if (providerType) {
      whereConditions.push(sql`provider_type = ${providerType}`)
    }

    // Combine WHERE conditions if any
    if (whereConditions.length > 0) {
      const whereClause = sql`WHERE ${sql.join(whereConditions, sql` AND `)}`
      countQuery = sql`${countQuery} ${whereClause}`
      dataQuery = sql`${dataQuery} ${whereClause}`
    }

    // Add ORDER BY and LIMIT clauses
    dataQuery = sql`
      ${dataQuery}
      ORDER BY created_at DESC
      LIMIT ${pageSize} OFFSET ${offset}
    `

    // Execute the queries
    const [countResult, dataResult] = await Promise.all([sql`${countQuery}`, sql`${dataQuery}`])

    const total = Number.parseInt(countResult[0].count)
    const totalPages = Math.ceil(total / pageSize)

    // Convert snake_case to camelCase
    const cronJobs = dataResult.map((job) => snakeToCamel(job))

    return successResponse(
      {
        cronJobs,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
        },
      },
      200,
    )
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch cronjobs", 500)
  }
}

// POST create a new cronjob
export async function POST(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const body = await request.json()

    // Validate required fields
    if (!body.recipient || !body.providerType || !body.payload) {
      return errorResponse("Recipient, providerType, and payload are required", 400, "INVALID_INPUT")
    }

    // Generate a unique job ID if not provided
    const jobId = body.jobId || `job-${crypto.randomUUID()}`

    // Calculate MD5 of payload for deduplication
    const payloadMd5 = crypto.createHash("md5").update(JSON.stringify(body.payload)).digest("hex")

    // Check for duplicate jobs with the same payload
    if (body.checkDuplicates !== false) {
      const duplicates = await sql`
        SELECT id FROM cronjobs 
        WHERE payload_md5 = ${payloadMd5} 
        AND recipient = ${body.recipient}
        AND provider_type = ${body.providerType}
        AND status IN ('pending', 'processing')
      `

      if (duplicates.length > 0) {
        return errorResponse("A similar job is already in the queue", 409, "CONFLICT")
      }
    }

    // Insert new cronjob
    const result = await sql`
      INSERT INTO cronjobs (
        job_id, recipient, created_by, provider_type, 
        template_id, payload, payload_md5, priority,
        max_retries, subject, category
      )
      VALUES (
        ${jobId}, 
        ${body.recipient}, 
        ${auth.user.userId}, 
        ${body.providerType}, 
        ${body.templateId || null}, 
        ${JSON.stringify(body.payload)}, 
        ${payloadMd5}, 
        ${body.priority || 0}, 
        ${body.maxRetries || 3}, 
        ${body.subject || null}, 
        ${body.category || null}
      )
      RETURNING *
    `

    const newCronJob = snakeToCamel(result[0])

    return successResponse({ cronJob: newCronJob }, 201)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to create cronjob", 500)
  }
}
