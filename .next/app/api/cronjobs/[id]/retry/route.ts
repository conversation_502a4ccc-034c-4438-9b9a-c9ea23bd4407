import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// POST to retry a failed cronjob
export async function POST(request: Request, { params }: { params: { id: string } }) {
  try {
    // Authenticate the request
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if cronjob exists and is in failed status
    const existingJob = await sql`
      SELECT * FROM cronjobs WHERE id = ${id}
    `

    if (existingJob.length === 0) {
      return errorResponse("Cronjob not found", 404, "NOT_FOUND")
    }

    const job = existingJob[0]

    if (job.status !== "failed") {
      return errorResponse("Only failed jobs can be retried", 400, "INVALID_INPUT")
    }

    // Reset the job for retry
    const result = await sql`
      UPDATE cronjobs
      SET 
        status = 'pending',
        retry_times = 0,
        next_retry_at = CURRENT_TIMESTAMP,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING *
    `

    const updatedJob = snakeToCamel(result[0])

    return successResponse(
      {
        message: "Cronjob queued for retry",
        cronJob: updatedJob,
      },
      200,
    )
  } catch (error) {
    console.error("Error retrying cronjob:", error)
    return errorResponse("Failed to retry cronjob", 500)
  }
}
