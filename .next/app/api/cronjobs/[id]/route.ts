import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET a specific cronjob by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const result = await sql`
      SELECT * FROM cronjobs
      WHERE id = ${id}
    `

    if (result.length === 0) {
      return errorResponse("Cronjob not found", 404, "NOT_FOUND")
    }

    const cronJob = snakeToCamel(result[0])

    return successResponse({ cronJob }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch cronjob", 500)
  }
}

// DELETE a cronjob
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if cronjob exists
    const existingJob = await sql`
      SELECT id FROM cronjobs WHERE id = ${id}
    `

    if (existingJob.length === 0) {
      return errorResponse("Cronjob not found", 404, "NOT_FOUND")
    }

    // Delete cronjob
    await sql`
      DELETE FROM cronjobs WHERE id = ${id}
    `

    return successResponse({ message: `Cronjob ${id} deleted successfully` }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to delete cronjob", 500)
  }
}
