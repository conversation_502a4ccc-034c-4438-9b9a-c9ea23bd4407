import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import crypto from "crypto"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// POST to create a test cronjob
export async function POST(request: Request) {
  try {
    // Authenticate the request
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const body = await request.json()

    // Validate required fields
    if (!body.recipient) {
      return errorResponse("Recipient email is required", 400, "INVALID_INPUT")
    }

    // Generate a unique job ID
    const jobId = `test-${crypto.randomUUID()}`

    // Create payload with test data
    const payload = {
      subject: body.subject || "Test Email from Inspection System",
      content:
        body.content ||
        `
        <h1>Test Email</h1>
        <p>This is a test email sent from the Inspection System at ${new Date().toISOString()}</p>
        <p>If you received this email, the cronjob system is working correctly.</p>
      `,
      testVariable: "This is a test variable",
      timestamp: new Date().toISOString(),
    }

    // Calculate MD5 of payload for deduplication
    const payloadMd5 = crypto.createHash("md5").update(JSON.stringify(payload)).digest("hex")

    // Insert new cronjob
    const result = await sql`
      INSERT INTO cronjobs (
        job_id, recipient, created_by, provider_type, 
        payload, payload_md5, subject, category
      )
      VALUES (
        ${jobId}, 
        ${body.recipient}, 
        ${auth.user.userId}, 
        'email', 
        ${JSON.stringify(payload)}, 
        ${payloadMd5}, 
        ${payload.subject}, 
        'test'
      )
      RETURNING *
    `

    const newCronJob = snakeToCamel(result[0])

    return successResponse(
      {
        message: "Test cronjob created successfully",
        cronJob: newCronJob,
      },
      201,
    )
  } catch (error) {
    console.error("Error creating test cronjob:", error)
    return errorResponse("Failed to create test cronjob", 500)
  }
}
