import { NextResponse } from "next/server"
import { processCronJobs } from "@/workers/cronjob-worker"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// POST to manually trigger the cronjob worker
export async function POST(request: Request) {
  try {
    // Authenticate the request (only admins should be able to trigger this)
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Check if user has admin role
    if (auth.user.role !== "admin") {
      return errorResponse("Admin access required", 403, "FORBIDDEN")
    }

    // Process the cronjobs
    const result = await processCronJobs()

    return successResponse(
      {
        message: "Cronjob worker triggered successfully",
        result,
      },
      200,
    )
  } catch (error) {
    console.error("Error triggering cronjob worker:", error)
    return errorResponse("Failed to trigger cronjob worker", 500)
  }
}
