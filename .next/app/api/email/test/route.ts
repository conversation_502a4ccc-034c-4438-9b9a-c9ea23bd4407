import { NextResponse } from "next/server"
import { authenticateRequest } from "@/middleware/auth"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { sendEmail } from "@/lib/email"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// POST to send a test email
export async function POST(request: Request) {
  try {
    // Authenticate the request
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const body = await request.json()

    // Validate required fields
    if (!body.to) {
      return errorResponse("Recipient email (to) is required", 400, "INVALID_INPUT")
    }

    // Set default values if not provided
    const subject = body.subject || "Test Email from Inspection System"
    const html =
      body.html ||
      `
      <h1>Test Email</h1>
      <p>This is a test email sent from the Inspection System at ${new Date().toISOString()}</p>
      <p>If you received this email, the email service is working correctly.</p>
    `
    const text = body.text || "This is a test email from the Inspection System."
    const from = body.from || undefined // Use default from name

    // Send the email
    const result = await sendEmail({
      to: body.to,
      subject,
      html,
      text,
      from,
    })

    if (result.success) {
      return successResponse(
        {
          message: "Test email sent successfully",
          result,
        },
        200,
      )
    } else {
      return errorResponse(`Failed to send test email: ${result.error}`, 500)
    }
  } catch (error) {
    console.error("Error sending test email:", error)
    return errorResponse("Failed to send test email", 500)
  }
}
