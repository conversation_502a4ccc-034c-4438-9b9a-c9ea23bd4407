import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET a specific template by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const result = await sql`
      SELECT id, name, type, status, value, custom_fields, custom_values, 
             created_at, created_by, updated_at, updated_by
      FROM templates
      WHERE id = ${id}
    `

    if (result.length === 0) {
      return errorResponse("Template not found", 404, "NOT_FOUND")
    }

    const template = snakeToCamel(result[0])

    return successResponse({ template }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch template", 500)
  }
}

// PUT update a template
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const body = await request.json()

    // Check if template exists
    const existingTemplate = await sql`
      SELECT id FROM templates WHERE id = ${id}
    `

    if (existingTemplate.length === 0) {
      return errorResponse("Template not found", 404, "NOT_FOUND")
    }

    // If name and type are being updated, check if the combination already exists
    if (body.name && body.type) {
      const nameTypeCheck = await sql`
        SELECT id FROM templates 
        WHERE name = ${body.name} AND type = ${body.type} AND id != ${id}
      `

      if (nameTypeCheck.length > 0) {
        return errorResponse("A template with this name and type already exists", 409, "CONFLICT")
      }
    }

    // Update template
    const result = await sql`
      UPDATE templates
      SET 
        name = COALESCE(${body.name}, name),
        type = COALESCE(${body.type}, type),
        status = COALESCE(${body.status}, status),
        value = COALESCE(${body.value}, value),
        custom_fields = COALESCE(${body.customFields ? JSON.stringify(body.customFields) : null}, custom_fields),
        custom_values = COALESCE(${body.customValues ? JSON.stringify(body.customValues) : null}, custom_values),
        updated_at = CURRENT_TIMESTAMP,
        updated_by = ${auth.user.userId}
      WHERE id = ${id}
      RETURNING id, name, type, status, value, custom_fields, custom_values, 
                created_at, created_by, updated_at, updated_by
    `

    const updatedTemplate = snakeToCamel(result[0])

    return successResponse({ template: updatedTemplate }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to update template", 500)
  }
}

// DELETE a template
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if template exists
    const existingTemplate = await sql`
      SELECT id FROM templates WHERE id = ${id}
    `

    if (existingTemplate.length === 0) {
      return errorResponse("Template not found", 404, "NOT_FOUND")
    }

    // Delete template
    await sql`
      DELETE FROM templates WHERE id = ${id}
    `

    return successResponse({ message: `Template ${id} deleted successfully` }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to delete template", 500)
  }
}
