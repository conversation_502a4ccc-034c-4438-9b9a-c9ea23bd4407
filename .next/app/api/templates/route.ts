import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all templates
export async function GET(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status")
    const type = searchParams.get("type")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "20")
    const offset = (page - 1) * pageSize

    // Build the query
    let countQuery = sql`SELECT COUNT(*) FROM templates`
    let dataQuery = sql`
      SELECT id, name, type, status, value, custom_fields, custom_values, 
             created_at, created_by, updated_at, updated_by
      FROM templates
    `

    // Add WHERE clauses if filters are provided
    const whereConditions = []

    if (status) {
      whereConditions.push(sql`status = ${status}`)
    }

    if (type) {
      whereConditions.push(sql`type = ${type}`)
    }

    // Combine WHERE conditions if any
    if (whereConditions.length > 0) {
      const whereClause = sql`WHERE ${sql.join(whereConditions, sql` AND `)}`
      countQuery = sql`${countQuery} ${whereClause}`
      dataQuery = sql`${dataQuery} ${whereClause}`
    }

    // Add ORDER BY and LIMIT clauses
    dataQuery = sql`
      ${dataQuery}
      ORDER BY name ASC
      LIMIT ${pageSize} OFFSET ${offset}
    `

    // Execute the queries
    const [countResult, dataResult] = await Promise.all([sql`${countQuery}`, sql`${dataQuery}`])

    const total = Number.parseInt(countResult[0].count)
    const totalPages = Math.ceil(total / pageSize)

    // Convert snake_case to camelCase
    const templates = dataResult.map((template) => snakeToCamel(template))

    return successResponse(
      {
        templates,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
        },
      },
      200,
    )
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch templates", 500)
  }
}

// POST create a new template
export async function POST(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const body = await request.json()

    // Validate required fields
    if (!body.name || !body.type) {
      return errorResponse("Name and type are required", 400, "INVALID_INPUT")
    }

    // Check if name and type combination already exists
    const existingTemplate = await sql`
      SELECT id FROM templates WHERE name = ${body.name} AND type = ${body.type}
    `

    if (existingTemplate.length > 0) {
      return errorResponse("A template with this name and type already exists", 409, "CONFLICT")
    }

    // Insert new template
    const result = await sql`
      INSERT INTO templates (
        name, type, status, value, custom_fields, custom_values, created_by
      )
      VALUES (
        ${body.name}, 
        ${body.type}, 
        ${body.status || "active"}, 
        ${body.value || null}, 
        ${body.customFields ? JSON.stringify(body.customFields) : null}, 
        ${body.customValues ? JSON.stringify(body.customValues) : null}, 
        ${auth.user.userId}
      )
      RETURNING id, name, type, status, value, custom_fields, custom_values, 
                created_at, created_by, updated_at, updated_by
    `

    const newTemplate = snakeToCamel(result[0])

    return successResponse({ template: newTemplate }, 201)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to create template", 500)
  }
}
