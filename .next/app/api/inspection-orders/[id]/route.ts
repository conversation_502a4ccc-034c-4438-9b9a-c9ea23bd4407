import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET a specific inspection order by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    const result = await sql`
      SELECT io.id, io.inspection_order_id, io.property_id, io.price, io.status, 
             io.notes, io.inspection_date, io.estimated_hours, io.assigned_to, 
             io.user_id, io.created_at, io.updated_at,
             p.name as property_name, 
             i.name as inspector_name
      FROM inspection_orders io
      LEFT JOIN properties p ON io.property_id = p.id
      LEFT JOIN inspectors i ON io.assigned_to = i.id
      WHERE io.id = ${id}
    `

    if (result.length === 0) {
      return NextResponse.json({ error: "Inspection order not found" }, { status: 404 })
    }

    const inspectionOrder = snakeToCamel(result[0])

    return NextResponse.json({ inspectionOrder }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch inspection order" }, { status: 500 })
  }
}

// PUT update an inspection order
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    const body = await request.json()

    // Check if inspection order exists
    const existingOrder = await sql`
      SELECT id FROM inspection_orders WHERE id = ${id}
    `

    if (existingOrder.length === 0) {
      return NextResponse.json({ error: "Inspection order not found" }, { status: 404 })
    }

    // Update inspection order
    const result = await sql`
      UPDATE inspection_orders
      SET 
        inspection_order_id = COALESCE(${body.inspectionOrderId}, inspection_order_id),
        property_id = COALESCE(${body.propertyId}, property_id),
        price = COALESCE(${body.price}, price),
        status = COALESCE(${body.status}, status),
        notes = COALESCE(${body.notes}, notes),
        inspection_date = COALESCE(${body.inspectionDate}, inspection_date),
        estimated_hours = COALESCE(${body.estimatedHours}, estimated_hours),
        assigned_to = COALESCE(${body.assignedTo}, assigned_to),
        user_id = COALESCE(${body.userId}, user_id),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, inspection_order_id, property_id, price, status, 
                notes, inspection_date, estimated_hours, assigned_to, 
                user_id, created_at, updated_at
    `

    const updatedOrder = snakeToCamel(result[0])

    return NextResponse.json({ inspectionOrder: updatedOrder }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to update inspection order" }, { status: 500 })
  }
}

// DELETE an inspection order
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    // Check if inspection order exists
    const existingOrder = await sql`
      SELECT id FROM inspection_orders WHERE id = ${id}
    `

    if (existingOrder.length === 0) {
      return NextResponse.json({ error: "Inspection order not found" }, { status: 404 })
    }

    // Delete inspection order
    await sql`
      DELETE FROM inspection_orders WHERE id = ${id}
    `

    return NextResponse.json({ message: `Inspection order ${id} deleted successfully` }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to delete inspection order" }, { status: 500 })
  }
}
