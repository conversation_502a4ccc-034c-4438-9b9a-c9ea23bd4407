import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET all inspection orders
export async function GET() {
  try {
    const result = await sql`
      SELECT io.id, io.inspection_order_id, io.property_id, io.price, io.status, 
             io.notes, io.inspection_date, io.estimated_hours, io.assigned_to, 
             io.user_id, io.created_at, io.updated_at,
             p.name as property_name, 
             i.name as inspector_name
      FROM inspection_orders io
      LEFT JOIN properties p ON io.property_id = p.id
      LEFT JOIN inspectors i ON io.assigned_to = i.id
      ORDER BY io.id
    `

    // Convert snake_case to camelCase
    const inspectionOrders = result.map((order) => snakeToCamel(order))

    return NextResponse.json({ inspectionOrders }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch inspection orders" }, { status: 500 })
  }
}

// POST create a new inspection order
export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.propertyId || !body.userId) {
      return NextResponse.json({ error: "Property ID and user ID are required" }, { status: 400 })
    }

    // Generate a unique inspection order ID if not provided
    const inspectionOrderId =
      body.inspectionOrderId || `INS-${new Date().getFullYear()}-${Math.floor(1000 + Math.random() * 9000)}`

    // Insert new inspection order
    const result = await sql`
      INSERT INTO inspection_orders (
        inspection_order_id, property_id, price, status, notes, 
        inspection_date, estimated_hours, assigned_to, user_id
      )
      VALUES (
        ${inspectionOrderId}, 
        ${body.propertyId}, 
        ${body.price || null}, 
        ${body.status || "pending"}, 
        ${body.notes || null}, 
        ${body.inspectionDate || null}, 
        ${body.estimatedHours || null}, 
        ${body.assignedTo || null}, 
        ${body.userId}
      )
      RETURNING id, inspection_order_id, property_id, price, status, 
                notes, inspection_date, estimated_hours, assigned_to, 
                user_id, created_at, updated_at
    `

    const newOrder = snakeToCamel(result[0])

    return NextResponse.json({ inspectionOrder: newOrder }, { status: 201 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to create inspection order" }, { status: 500 })
  }
}
