import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET all inspection orders for a specific property
export async function GET(request: Request, { params }: { params: { propertyId: string } }) {
  try {
    const propertyId = Number.parseInt(params.propertyId)

    if (isNaN(propertyId)) {
      return NextResponse.json({ error: "Invalid property ID format" }, { status: 400 })
    }

    const result = await sql`
      SELECT io.id, io.inspection_order_id, io.property_id, io.price, io.status, 
             io.notes, io.inspection_date, io.estimated_hours, io.assigned_to, 
             io.user_id, io.created_at, io.updated_at,
             p.name as property_name, 
             i.name as inspector_name
      FROM inspection_orders io
      LEFT JOIN properties p ON io.property_id = p.id
      LEFT JOIN inspectors i ON io.assigned_to = i.id
      WHERE io.property_id = ${propertyId}
      ORDER BY io.inspection_date DESC
    `

    // Convert snake_case to camelCase
    const inspectionOrders = result.map((order) => snakeToCamel(order))

    return NextResponse.json({ inspectionOrders }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch property inspection orders" }, { status: 500 })
  }
}
