import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET a specific custom field by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const result = await sql`
      SELECT id, field_name, field_type, field_value, status, 
             created_at, created_by, updated_at, updated_by
      FROM custom_fields
      WHERE id = ${id}
    `

    if (result.length === 0) {
      return errorResponse("Custom field not found", 404, "NOT_FOUND")
    }

    const customField = snakeToCamel(result[0])

    return successResponse({ customField }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch custom field", 500)
  }
}

// PUT update a custom field
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    const body = await request.json()

    // Check if custom field exists
    const existingField = await sql`
      SELECT id FROM custom_fields WHERE id = ${id}
    `

    if (existingField.length === 0) {
      return errorResponse("Custom field not found", 404, "NOT_FOUND")
    }

    // If name is being updated, check if it's already in use
    if (body.fieldName) {
      const nameCheck = await sql`
        SELECT id FROM custom_fields 
        WHERE field_name = ${body.fieldName} AND id != ${id}
      `

      if (nameCheck.length > 0) {
        return errorResponse("A custom field with this name already exists", 409, "CONFLICT")
      }
    }

    // Update custom field
    const result = await sql`
      UPDATE custom_fields
      SET 
        field_name = COALESCE(${body.fieldName}, field_name),
        field_type = COALESCE(${body.fieldType}, field_type),
        field_value = COALESCE(${body.fieldValue ? JSON.stringify(body.fieldValue) : null}, field_value),
        status = COALESCE(${body.status}, status),
        updated_at = CURRENT_TIMESTAMP,
        updated_by = ${auth.user.userId}
      WHERE id = ${id}
      RETURNING id, field_name, field_type, field_value, status, 
                created_at, created_by, updated_at, updated_by
    `

    const updatedCustomField = snakeToCamel(result[0])

    return successResponse({ customField: updatedCustomField }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to update custom field", 500)
  }
}

// DELETE a custom field (soft delete by setting status to 'deleted')
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
    }

    // Check if custom field exists
    const existingField = await sql`
      SELECT id FROM custom_fields WHERE id = ${id}
    `

    if (existingField.length === 0) {
      return errorResponse("Custom field not found", 404, "NOT_FOUND")
    }

    // Soft delete by setting status to 'deleted'
    const result = await sql`
      UPDATE custom_fields
      SET 
        status = 'deleted',
        updated_at = CURRENT_TIMESTAMP,
        updated_by = ${auth.user.userId}
      WHERE id = ${id}
      RETURNING id, field_name, field_type, field_value, status, 
                created_at, created_by, updated_at, updated_by
    `

    const deletedCustomField = snakeToCamel(result[0])

    return successResponse(
      {
        message: `Custom field ${id} marked as deleted`,
        customField: deletedCustomField,
      },
      200,
    )
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to delete custom field", 500)
  }
}
