import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { authenticateRequest } from "@/middleware/auth"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all custom fields
export async function GET(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Get query parameters for filtering
    const { searchParams } = new URL(request.url)
    const status = searchParams.get("status")
    const type = searchParams.get("type")
    const page = Number.parseInt(searchParams.get("page") || "1")
    const pageSize = Number.parseInt(searchParams.get("pageSize") || "20")
    const offset = (page - 1) * pageSize
    const sortBy = searchParams.get("sortBy") || "field_name"
    const sortOrder = searchParams.get("sortOrder") || "asc"

    // Build the query
    let countQuery = sql`SELECT COUNT(*) FROM custom_fields`
    let dataQuery = sql`
      SELECT id, field_name, field_type, field_value, status, 
             created_at, created_by, updated_at, updated_by
      FROM custom_fields
    `

    // Add WHERE clauses if filters are provided
    const whereConditions = []

    if (status) {
      whereConditions.push(sql`status = ${status}`)
    }

    if (type) {
      whereConditions.push(sql`field_type = ${type}`)
    }

    // Combine WHERE conditions if any
    if (whereConditions.length > 0) {
      // Instead of using sql.join(), we'll build the WHERE clause manually
      if (whereConditions.length === 1) {
        countQuery = sql`${countQuery} WHERE ${whereConditions[0]}`
        dataQuery = sql`${dataQuery} WHERE ${whereConditions[0]}`
      } else if (whereConditions.length === 2) {
        countQuery = sql`${countQuery} WHERE ${whereConditions[0]} AND ${whereConditions[1]}`
        dataQuery = sql`${dataQuery} WHERE ${whereConditions[0]} AND ${whereConditions[1]}`
      }
      // Add more conditions if needed
    }

    // Add ORDER BY and LIMIT clauses
    // Instead of using sql.identifier, we'll use a switch statement to handle different sort columns
    let orderByClause
    switch (sortBy) {
      case "field_name":
        orderByClause = sortOrder === "asc" ? sql`field_name ASC` : sql`field_name DESC`
        break
      case "field_type":
        orderByClause = sortOrder === "asc" ? sql`field_type ASC` : sql`field_type DESC`
        break
      case "status":
        orderByClause = sortOrder === "asc" ? sql`status ASC` : sql`status DESC`
        break
      case "created_at":
        orderByClause = sortOrder === "asc" ? sql`created_at ASC` : sql`created_at DESC`
        break
      default:
        orderByClause = sql`field_name ASC`
    }

    dataQuery = sql`
      ${dataQuery}
      ORDER BY ${orderByClause}
      LIMIT ${pageSize} OFFSET ${offset}
    `

    // Execute the queries
    const [countResult, dataResult] = await Promise.all([sql`${countQuery}`, sql`${dataQuery}`])

    const total = Number.parseInt(countResult[0].count)
    const totalPages = Math.ceil(total / pageSize)

    // Convert snake_case to camelCase
    const customFields = dataResult.map((field) => snakeToCamel(field))

    return successResponse(
      {
        customFields,
        pagination: {
          page,
          pageSize,
          total,
          totalPages,
        },
      },
      200,
    )
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch custom fields", 500)
  }
}

// POST create a new custom field
export async function POST(request: Request) {
  try {
    const auth = await authenticateRequest(request)

    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    const body = await request.json()

    // Validate required fields
    if (!body.fieldName || !body.fieldType) {
      return errorResponse("Field name and field type are required", 400, "INVALID_INPUT")
    }

    // Check if name already exists
    const existingField = await sql`
      SELECT id FROM custom_fields WHERE field_name = ${body.fieldName}
    `

    if (existingField.length > 0) {
      return errorResponse("A custom field with this name already exists", 409, "CONFLICT")
    }

    // Insert new custom field
    const result = await sql`
      INSERT INTO custom_fields (
        field_name, field_type, field_value, status, created_by
      )
      VALUES (
        ${body.fieldName}, 
        ${body.fieldType}, 
        ${body.fieldValue ? JSON.stringify(body.fieldValue) : null}, 
        ${body.status || "active"}, 
        ${auth.user.userId}
      )
      RETURNING id, field_name, field_type, field_value, status, 
                created_at, created_by, updated_at, updated_by
    `

    const newCustomField = snakeToCamel(result[0])

    return successResponse({ customField: newCustomField }, 201)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to create custom field", 500)
  }
}
