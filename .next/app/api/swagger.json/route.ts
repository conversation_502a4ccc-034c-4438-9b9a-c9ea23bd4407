import { NextResponse } from "next/server"
import { CORS_HEADERS } from "@/lib/api-response"

export async function GET() {
  try {
    // Create a comprehensive OpenAPI specification for all API endpoints
    const openApiDocument = {
      openapi: "3.0.0",
      info: {
        title: "Inspection API",
        version: "1.0.0",
        description: "Complete API documentation for the Inspection System",
      },
      servers: [
        {
          url: "/",
          description: "Current server",
        },
      ],
      tags: [
        {
          name: "Authentication",
          description: "Authentication operations",
        },
        {
          name: "Users",
          description: "User management operations",
        },
        {
          name: "Orders",
          description: "Inspection order operations",
        },
        {
          name: "Inspectors",
          description: "Inspector operations",
        },
        {
          name: "Properties",
          description: "Property operations",
        },
        {
          name: "Schedules",
          description: "Schedule operations",
        },
        {
          name: "CustomFields",
          description: "Custom field operations",
        },
        {
          name: "Templates",
          description: "Template operations",
        },
        {
          name: "Setting<PERSON>",
          description: "Settings operations",
        },
        {
          name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
          description: "Background job operations",
        },
        {
          name: "Email",
          description: "Email operations",
        },
      ],
      paths: {
        // Authentication endpoints
        "/api/auth/login": {
          post: {
            operationId: "loginUser",
            tags: ["Authentication"],
            summary: "Login to the system",
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      email: {
                        type: "string",
                        format: "email",
                      },
                      password: {
                        type: "string",
                      },
                    },
                    required: ["email", "password"],
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "Successful login",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/AuthResponse",
                    },
                  },
                },
              },
              "401": {
                description: "Invalid credentials",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/auth/register": {
          post: {
            operationId: "registerUser",
            tags: ["Authentication"],
            summary: "Register a new user",
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/UserRegistration",
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "User registered successfully",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/AuthResponse",
                    },
                  },
                },
              },
              "409": {
                description: "Email already in use",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/auth/refresh": {
          post: {
            operationId: "refreshToken",
            tags: ["Authentication"],
            summary: "Refresh access token",
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      refreshToken: {
                        type: "string",
                      },
                    },
                    required: ["refreshToken"],
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "Tokens refreshed successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            accessToken: { type: "string" },
                            refreshToken: { type: "string" },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Invalid refresh token",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/auth/verify": {
          post: {
            operationId: "verifyToken",
            tags: ["Authentication"],
            summary: "Verify JWT token",
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      token: {
                        type: "string",
                      },
                    },
                    required: ["token"],
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "Token is valid",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            valid: { type: "boolean" },
                            user: {
                              type: "object",
                              properties: {
                                userId: { type: "integer" },
                                email: { type: "string" },
                                role: { type: "string" },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Invalid token",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/auth/logout": {
          post: {
            operationId: "logoutUser",
            tags: ["Authentication"],
            summary: "Logout user",
            responses: {
              "200": {
                description: "Logged out successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        "/api/auth/me": {
          get: {
            operationId: "getCurrentUser",
            tags: ["Authentication"],
            summary: "Get current user information",
            security: [{ bearerAuth: [] }],
            responses: {
              "200": {
                description: "Current user information",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            user: {
                              $ref: "#/components/schemas/User",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // User endpoints
        "/api/users": {
          get: {
            operationId: "getUsers",
            tags: ["Users"],
            summary: "Get all users (admin only)",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "page",
                in: "query",
                schema: { type: "integer", default: 1 },
                description: "Page number",
              },
              {
                name: "pageSize",
                in: "query",
                schema: { type: "integer", default: 10 },
                description: "Number of items per page",
              },
            ],
            responses: {
              "200": {
                description: "List of users",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            users: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/User",
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
              "403": {
                description: "Forbidden - Admin access required",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createUser",
            tags: ["Users"],
            summary: "Create a new user",
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/UserRegistration",
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "User created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            user: {
                              $ref: "#/components/schemas/User",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "409": {
                description: "Email already in use",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/users/{id}": {
          get: {
            operationId: "getUserById",
            tags: ["Users"],
            summary: "Get a user by ID",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "User ID",
              },
            ],
            responses: {
              "200": {
                description: "User details",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            user: {
                              $ref: "#/components/schemas/User",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "User not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          put: {
            operationId: "updateUser",
            tags: ["Users"],
            summary: "Update a user",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "User ID",
              },
            ],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      email: { type: "string", format: "email" },
                      role: { type: "string", enum: ["user", "admin", "inspector"] },
                    },
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "User updated successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            user: {
                              $ref: "#/components/schemas/User",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "User not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          delete: {
            operationId: "deleteUser",
            tags: ["Users"],
            summary: "Delete a user",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "User ID",
              },
            ],
            responses: {
              "200": {
                description: "User deleted successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "User not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Email endpoints
        "/api/email/test": {
          post: {
            operationId: "sendTestEmail",
            tags: ["Email"],
            summary: "Send a test email",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      to: {
                        type: "string",
                        format: "email",
                        description: "Recipient email address",
                      },
                      subject: {
                        type: "string",
                        description: "Email subject",
                      },
                      html: {
                        type: "string",
                        description: "HTML content of the email",
                      },
                      text: {
                        type: "string",
                        description: "Plain text content of the email",
                      },
                      from: {
                        type: "string",
                        description: "Sender name/email (optional)",
                      },
                    },
                    required: ["to"],
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "Email sent successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                            result: {
                              type: "object",
                              properties: {
                                success: { type: "boolean" },
                                messageId: { type: "string" },
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
              "500": {
                description: "Failed to send email",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Order endpoints
        "/api/orders": {
          get: {
            operationId: "getOrders",
            tags: ["Orders"],
            summary: "Get all inspection orders",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "page",
                in: "query",
                schema: { type: "integer", default: 1 },
                description: "Page number",
              },
              {
                name: "pageSize",
                in: "query",
                schema: { type: "integer", default: 10 },
                description: "Number of items per page",
              },
              {
                name: "status",
                in: "query",
                schema: { type: "string" },
                description: "Filter by status",
              },
              {
                name: "search",
                in: "query",
                schema: { type: "string" },
                description: "Search term",
              },
              {
                name: "sortBy",
                in: "query",
                schema: { type: "string", default: "created_at" },
                description: "Field to sort by",
              },
              {
                name: "sortOrder",
                in: "query",
                schema: { type: "string", enum: ["asc", "desc"], default: "desc" },
                description: "Sort order",
              },
            ],
            responses: {
              "200": {
                description: "List of orders",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            orders: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/Order",
                              },
                            },
                            pagination: {
                              $ref: "#/components/schemas/Pagination",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createOrder",
            tags: ["Orders"],
            summary: "Create a new inspection order",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/OrderCreate",
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Order created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            order: {
                              $ref: "#/components/schemas/Order",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/orders/{id}": {
          get: {
            operationId: "getOrderById",
            tags: ["Orders"],
            summary: "Get an inspection order by ID",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Order ID",
              },
            ],
            responses: {
              "200": {
                description: "Order details",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            order: {
                              $ref: "#/components/schemas/Order",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Order not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          put: {
            operationId: "updateOrder",
            tags: ["Orders"],
            summary: "Update an inspection order",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Order ID",
              },
            ],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/OrderUpdate",
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "Order updated successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            order: {
                              $ref: "#/components/schemas/Order",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Order not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          delete: {
            operationId: "deleteOrder",
            tags: ["Orders"],
            summary: "Delete an inspection order",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Order ID",
              },
            ],
            responses: {
              "200": {
                description: "Order deleted successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Order not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/orders/{id}/change-status": {
          post: {
            operationId: "changeOrderStatus",
            tags: ["Orders"],
            summary: "Change the status of an inspection order",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Order ID",
              },
            ],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      status: {
                        type: "string",
                        enum: [
                          "pending",
                          "inprogress",
                          "completed",
                          "scheduled",
                          "paid",
                          "cancelled",
                          "inspected",
                          "reportsent",
                        ],
                      },
                      notes: { type: "string" },
                    },
                    required: ["status"],
                  },
                },
              },
            },
            responses: {
              "200": {
                description: "Status changed successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                            order: {
                              $ref: "#/components/schemas/Order",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Order not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Inspector endpoints
        "/api/inspectors": {
          get: {
            operationId: "getInspectors",
            tags: ["Inspectors"],
            summary: "Get all inspectors",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "status",
                in: "query",
                schema: { type: "string" },
                description: "Filter by status",
              },
            ],
            responses: {
              "200": {
                description: "List of inspectors",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            inspectors: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/Inspector",
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createInspector",
            tags: ["Inspectors"],
            summary: "Create a new inspector",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      email: { type: "string", format: "email" },
                      phone: { type: "string" },
                      address: { type: "string" },
                      status: { type: "string", enum: ["active", "inactive"] },
                      inspectionStatus: { type: "string", enum: ["available", "busy", "on_leave"] },
                    },
                    required: ["name", "email"],
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Inspector created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            inspector: {
                              $ref: "#/components/schemas/Inspector",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Property endpoints
        "/api/properties": {
          get: {
            operationId: "getProperties",
            tags: ["Properties"],
            summary: "Get all properties",
            security: [{ bearerAuth: [] }],
            responses: {
              "200": {
                description: "List of properties",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            properties: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/Property",
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createProperty",
            tags: ["Properties"],
            summary: "Create a new property",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      address: { type: "string" },
                      latitude: { type: "number" },
                      longitude: { type: "number" },
                      squareFeet: { type: "number" },
                      complexity: { type: "string" },
                      tags: { type: "array", items: { type: "string" } },
                      notes: { type: "string" },
                      userId: { type: "integer" },
                    },
                    required: ["name", "address"],
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Property created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            property: {
                              $ref: "#/components/schemas/Property",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Custom Fields endpoints
        "/api/custom-fields": {
          get: {
            operationId: "getCustomFields",
            tags: ["CustomFields"],
            summary: "Get all custom fields",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "status",
                in: "query",
                schema: { type: "string", enum: ["active", "deactive", "deleted"] },
                description: "Filter by status",
              },
              {
                name: "type",
                in: "query",
                schema: { type: "string" },
                description: "Filter by type",
              },
            ],
            responses: {
              "200": {
                description: "List of custom fields",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            customFields: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/CustomField",
                              },
                            },
                            pagination: {
                              $ref: "#/components/schemas/Pagination",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createCustomField",
            tags: ["CustomFields"],
            summary: "Create a new custom field",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      type: { type: "string" },
                      controlType: { type: "string" },
                      defaultValue: { type: "string" },
                      status: { type: "string", enum: ["active", "deactive", "deleted"] },
                    },
                    required: ["name", "type", "controlType"],
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Custom field created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            customField: {
                              $ref: "#/components/schemas/CustomField",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Templates endpoints
        "/api/templates": {
          get: {
            operationId: "getTemplates",
            tags: ["Templates"],
            summary: "Get all templates",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "status",
                in: "query",
                schema: { type: "string", enum: ["active", "deactive", "deleted"] },
                description: "Filter by status",
              },
              {
                name: "type",
                in: "query",
                schema: { type: "string" },
                description: "Filter by type",
              },
            ],
            responses: {
              "200": {
                description: "List of templates",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            templates: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/Template",
                              },
                            },
                            pagination: {
                              $ref: "#/components/schemas/Pagination",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createTemplate",
            tags: ["Templates"],
            summary: "Create a new template",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      type: { type: "string" },
                      status: { type: "string", enum: ["active", "deactive", "deleted"] },
                      value: { type: "string" },
                      customFields: { type: "array", items: { type: "string" } },
                      customValues: { type: "object" },
                    },
                    required: ["name", "type"],
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Template created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            template: {
                              $ref: "#/components/schemas/Template",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Settings endpoints
        "/api/settings": {
          get: {
            operationId: "getSettings",
            tags: ["Settings"],
            summary: "Get all settings",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "type",
                in: "query",
                schema: { type: "string" },
                description: "Filter by type",
              },
            ],
            responses: {
              "200": {
                description: "List of settings",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            settings: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/Setting",
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createSetting",
            tags: ["Settings"],
            summary: "Create a new setting",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      name: { type: "string" },
                      type: { type: "string" },
                      value: { type: "string" },
                    },
                    required: ["name", "type", "value"],
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Setting created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            setting: {
                              $ref: "#/components/schemas/Setting",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
              "403": {
                description: "Forbidden - Admin access required",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // CronJobs endpoints
        "/api/cronjobs": {
          get: {
            operationId: "getCronJobs",
            tags: ["CronJobs"],
            summary: "Get all cron jobs",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "status",
                in: "query",
                schema: { type: "string", enum: ["pending", "processing", "sent", "failed"] },
                description: "Filter by status",
              },
              {
                name: "providerType",
                in: "query",
                schema: { type: "string", enum: ["email", "sms"] },
                description: "Filter by provider type",
              },
              {
                name: "page",
                in: "query",
                schema: { type: "integer", default: 1 },
                description: "Page number",
              },
              {
                name: "pageSize",
                in: "query",
                schema: { type: "integer", default: 20 },
                description: "Number of items per page",
              },
            ],
            responses: {
              "200": {
                description: "List of cron jobs",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            cronJobs: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/CronJob",
                              },
                            },
                            pagination: {
                              $ref: "#/components/schemas/Pagination",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createCronJob",
            tags: ["CronJobs"],
            summary: "Create a new cron job",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    type: "object",
                    properties: {
                      jobId: { type: "string" },
                      recipient: { type: "string" },
                      providerType: { type: "string", enum: ["email", "sms"] },
                      templateId: { type: "integer" },
                      payload: { type: "object" },
                      subject: { type: "string" },
                      category: { type: "string" },
                      priority: { type: "integer", default: 0 },
                      maxRetries: { type: "integer", default: 3 },
                    },
                    required: ["recipient", "providerType", "payload"],
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Cron job created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            cronJob: {
                              $ref: "#/components/schemas/CronJob",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/cronjobs/{id}": {
          get: {
            operationId: "getCronJobById",
            tags: ["CronJobs"],
            summary: "Get a cron job by ID",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Cron job ID",
              },
            ],
            responses: {
              "200": {
                description: "Cron job details",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            cronJob: {
                              $ref: "#/components/schemas/CronJob",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Cron job not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          delete: {
            operationId: "deleteCronJob",
            tags: ["CronJobs"],
            summary: "Delete a cron job",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Cron job ID",
              },
            ],
            responses: {
              "200": {
                description: "Cron job deleted successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Cron job not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
        "/api/cronjobs/{id}/retry": {
          post: {
            operationId: "retryCronJob",
            tags: ["CronJobs"],
            summary: "Retry a failed cron job",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "id",
                in: "path",
                required: true,
                schema: { type: "integer" },
                description: "Cron job ID",
              },
            ],
            responses: {
              "200": {
                description: "Cron job queued for retry",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            message: { type: "string" },
                            cronJob: {
                              $ref: "#/components/schemas/CronJob",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "404": {
                description: "Cron job not found",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
              "400": {
                description: "Job is not in failed status",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },

        // Inspector Schedule endpoints
        "/api/schedules": {
          get: {
            operationId: "getSchedules",
            tags: ["Schedules"],
            summary: "Get all schedules",
            security: [{ bearerAuth: [] }],
            parameters: [
              {
                name: "inspectorId",
                in: "query",
                schema: { type: "integer" },
                description: "Filter by inspector ID",
              },
              {
                name: "date",
                in: "query",
                schema: { type: "string", format: "date" },
                description: "Filter by date (YYYY-MM-DD)",
              },
              {
                name: "available",
                in: "query",
                schema: { type: "boolean" },
                description: "Filter by availability",
              },
            ],
            responses: {
              "200": {
                description: "List of schedules",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            schedules: {
                              type: "array",
                              items: {
                                $ref: "#/components/schemas/Schedule",
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "401": {
                description: "Unauthorized",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
          post: {
            operationId: "createSchedule",
            tags: ["Schedules"],
            summary: "Create a new schedule",
            security: [{ bearerAuth: [] }],
            requestBody: {
              required: true,
              content: {
                "application/json": {
                  schema: {
                    $ref: "#/components/schemas/ScheduleCreate",
                  },
                },
              },
            },
            responses: {
              "201": {
                description: "Schedule created successfully",
                content: {
                  "application/json": {
                    schema: {
                      type: "object",
                      properties: {
                        success: { type: "boolean", example: true },
                        data: {
                          type: "object",
                          properties: {
                            schedule: {
                              $ref: "#/components/schemas/Schedule",
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
              "400": {
                description: "Invalid input or scheduling conflict",
                content: {
                  "application/json": {
                    schema: {
                      $ref: "#/components/schemas/ErrorResponse",
                    },
                  },
                },
              },
            },
          },
        },
      },
      components: {
        schemas: {
          User: {
            type: "object",
            properties: {
              id: { type: "integer" },
              name: { type: "string" },
              email: { type: "string", format: "email" },
              role: { type: "string", enum: ["user", "admin", "inspector"] },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
          UserRegistration: {
            type: "object",
            properties: {
              name: { type: "string" },
              email: { type: "string", format: "email" },
              password: { type: "string" },
              role: { type: "string", enum: ["user", "admin", "inspector"] },
            },
            required: ["name", "email", "password"],
          },
          AuthResponse: {
            type: "object",
            properties: {
              success: { type: "boolean", example: true },
              data: {
                type: "object",
                properties: {
                  user: {
                    type: "object",
                    properties: {
                      id: { type: "integer" },
                      name: { type: "string" },
                      email: { type: "string" },
                      role: { type: "string" },
                    },
                  },
                  accessToken: { type: "string" },
                  refreshToken: { type: "string" },
                },
              },
            },
          },
          Order: {
            type: "object",
            properties: {
              id: { type: "integer" },
              inspectionOrderId: { type: "string" },
              clientName: { type: "string" },
              clientPhone: { type: "string" },
              clientEmail: { type: "string", format: "email" },
              propertyAddress: { type: "string" },
              propertyType: {
                type: "string",
                enum: ["single", "family", "duplex", "triplex", "quadplex", "commercial", "apartment"],
              },
              status: {
                type: "string",
                enum: [
                  "pending",
                  "inprogress",
                  "completed",
                  "scheduled",
                  "paid",
                  "cancelled",
                  "inspected",
                  "reportsent",
                ],
              },
              inspectionDate: { type: "string", format: "date-time" },
              inspectionFees: { type: "number" },
              assignedInspectorIds: { type: "array", items: { type: "integer" } },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
          OrderCreate: {
            type: "object",
            properties: {
              clientName: { type: "string" },
              clientPhone: { type: "string" },
              clientEmail: { type: "string", format: "email" },
              propertyAddress: { type: "string" },
              propertyType: {
                type: "string",
                enum: ["single", "family", "duplex", "triplex", "quadplex", "commercial", "apartment"],
              },
              yearBuilt: { type: "integer" },
              inspectionFees: { type: "number" },
              inspectionDate: { type: "string", format: "date-time" },
              assignedInspectorIds: { type: "array", items: { type: "integer" } },
            },
            required: ["clientName", "propertyAddress"],
          },
          OrderUpdate: {
            type: "object",
            properties: {
              clientName: { type: "string" },
              clientPhone: { type: "string" },
              clientEmail: { type: "string", format: "email" },
              propertyAddress: { type: "string" },
              propertyType: {
                type: "string",
                enum: ["single", "family", "duplex", "triplex", "quadplex", "commercial", "apartment"],
              },
              status: {
                type: "string",
                enum: [
                  "pending",
                  "inprogress",
                  "completed",
                  "scheduled",
                  "paid",
                  "cancelled",
                  "inspected",
                  "reportsent",
                ],
              },
              inspectionDate: { type: "string", format: "date-time" },
              inspectionFees: { type: "number" },
              assignedInspectorIds: { type: "array", items: { type: "integer" } },
            },
          },
          Inspector: {
            type: "object",
            properties: {
              id: { type: "integer" },
              name: { type: "string" },
              email: { type: "string", format: "email" },
              phone: { type: "string" },
              address: { type: "string" },
              points: { type: "integer" },
              status: { type: "string", enum: ["active", "inactive"] },
              inspectionStatus: { type: "string", enum: ["available", "busy", "on_leave"] },
              totalInspections: { type: "integer" },
              tags: { type: "array", items: { type: "string" } },
              notes: { type: "string" },
              userId: { type: "integer" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
          Property: {
            type: "object",
            properties: {
              id: { type: "integer" },
              name: { type: "string" },
              address: { type: "string" },
              latitude: { type: "number" },
              longitude: { type: "number" },
              squareFeet: { type: "number" },
              complexity: { type: "string" },
              tags: { type: "array", items: { type: "string" } },
              notes: { type: "string" },
              userId: { type: "integer" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
            },
          },
          CustomField: {
            type: "object",
            properties: {
              id: { type: "integer" },
              name: { type: "string" },
              type: { type: "string" },
              controlType: { type: "string" },
              defaultValue: { type: "string" },
              status: { type: "string", enum: ["active", "deactive", "deleted"] },
              createdAt: { type: "string", format: "date-time" },
              createdBy: { type: "integer" },
              updatedAt: { type: "string", format: "date-time" },
              updatedBy: { type: "integer" },
            },
          },
          Template: {
            type: "object",
            properties: {
              id: { type: "integer" },
              name: { type: "string" },
              type: { type: "string" },
              status: { type: "string", enum: ["active", "deactive", "deleted"] },
              value: { type: "string" },
              customFields: { type: "array", items: { type: "string" } },
              customValues: { type: "object" },
              createdAt: { type: "string", format: "date-time" },
              createdBy: { type: "integer" },
              updatedAt: { type: "string", format: "date-time" },
              updatedBy: { type: "integer" },
            },
          },
          Setting: {
            type: "object",
            properties: {
              id: { type: "integer" },
              name: { type: "string" },
              type: { type: "string" },
              value: { type: "string" },
              createdAt: { type: "string", format: "date-time" },
              createdBy: { type: "integer" },
              updatedAt: { type: "string", format: "date-time" },
              updatedBy: { type: "integer" },
            },
          },
          CronJob: {
            type: "object",
            properties: {
              id: { type: "integer" },
              jobId: { type: "string" },
              recipient: { type: "string" },
              createdBy: { type: "integer" },
              createdAt: { type: "string", format: "date-time" },
              providerType: { type: "string", enum: ["email", "sms"] },
              templateId: { type: "integer" },
              queuedDate: { type: "string", format: "date-time" },
              sentDate: { type: "string", format: "date-time" },
              status: { type: "string", enum: ["pending", "processing", "sent", "failed"] },
              lastResult: { type: "string" },
              payload: { type: "object" },
              payloadMd5: { type: "string" },
              retryTimes: { type: "integer" },
              maxRetries: { type: "integer" },
              nextRetryAt: { type: "string", format: "date-time" },
              priority: { type: "integer" },
              subject: { type: "string" },
              category: { type: "string" },
            },
          },
          Schedule: {
            type: "object",
            properties: {
              id: { type: "integer" },
              inspectorId: { type: "integer" },
              date: { type: "string", format: "date" },
              startTime: { type: "string", format: "time" },
              endTime: { type: "string", format: "time" },
              available: { type: "boolean" },
              inspectionOrderId: { type: "integer" },
              createdAt: { type: "string", format: "date-time" },
              updatedAt: { type: "string", format: "date-time" },
              inspectorName: { type: "string" },
            },
          },
          ScheduleCreate: {
            type: "object",
            properties: {
              inspectorId: { type: "integer" },
              date: { type: "string", format: "date" },
              startTime: { type: "string", format: "time" },
              endTime: { type: "string", format: "time" },
              available: { type: "boolean" },
              inspectionOrderId: { type: "integer" },
            },
            required: ["inspectorId", "date", "startTime", "endTime"],
          },
          Pagination: {
            type: "object",
            properties: {
              page: { type: "integer" },
              pageSize: { type: "integer" },
              total: { type: "integer" },
              totalPages: { type: "integer" },
            },
          },
          ErrorResponse: {
            type: "object",
            properties: {
              success: { type: "boolean", example: false },
              statusCode: { type: "integer" },
              errorCode: { type: "string" },
              error: { type: "string" },
              data: { type: "null" },
            },
          },
        },
        securitySchemes: {
          bearerAuth: {
            type: "http",
            scheme: "bearer",
            bearerFormat: "JWT",
          },
        },
      },
    }

    return NextResponse.json(openApiDocument, {
      headers: CORS_HEADERS,
    })
  } catch (error) {
    console.error("Error generating OpenAPI document:", error)

    // Return a valid JSON response even in case of error
    return NextResponse.json(
      {
        openapi: "3.0.0",
        info: {
          title: "Inspection API (Error)",
          version: "1.0.0",
          description: `Error generating API documentation: ${error instanceof Error ? error.message : "Unknown error"}`,
        },
        paths: {},
      },
      {
        status: 500,
        headers: CORS_HEADERS,
      },
    )
  }
}
