import { verifyJWT } from "@/lib/jwt"
import { successResponse, errorResponse } from "@/lib/api-response"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.token) {
      return errorResponse("Token is required", 400, "INVALID_INPUT")
    }

    // Verify the token
    const payload = await verifyJWT(body.token)

    // Return the payload
    return successResponse(
      {
        valid: true,
        user: {
          userId: payload.userId,
          email: payload.email,
          role: payload.role,
        },
      },
      200,
    )
  } catch (error) {
    console.error("Token verification error:", error)
    return errorResponse("Invalid token", 401, "UNAUTHORIZED")
  }
}
