import { sql, snakeToCamel } from "@/lib/db"
import { authenticateRequest, unauthorizedResponse } from "@/middleware/auth"
import { successResponse, errorResponse } from "@/lib/api-response"

export async function GET(request: Request) {
  // Authenticate the request
  const auth = await authenticateRequest(request)

  if (!auth.authenticated) {
    return unauthorizedResponse(auth.error)
  }

  try {
    // Get the user from the database
    const result = await sql`
      SELECT id, name, email, role, created_at, updated_at
      FROM users
      WHERE id = ${auth.user.userId}
    `

    if (result.length === 0) {
      return errorResponse("User not found", 404, "NOT_FOUND")
    }

    const user = snakeToCamel(result[0])

    // Return the user info (excluding password)
    return successResponse(
      {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      },
      200,
    )
  } catch (error) {
    console.error("Get current user error:", error)
    return errorResponse("Failed to get user information", 500)
  }
}
