import { successResponse } from "@/lib/api-response"

export async function POST() {
  // In a stateless JWT system, the client is responsible for discarding tokens
  // Server-side, we don't need to do anything special for logout
  // In a real app with refresh token tracking, you would invalidate the refresh token here

  return successResponse(
    {
      message: "Logged out successfully",
    },
    200,
  )
}
