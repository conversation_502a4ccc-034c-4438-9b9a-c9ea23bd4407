import { verifyJWT, generateTokens } from "@/lib/jwt"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse } from "@/lib/api-response"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.refreshToken) {
      return errorResponse("Refresh token is required", 400, "INVALID_INPUT")
    }

    // Verify the refresh token
    const payload = await verifyJWT(body.refreshToken)

    // Get the user from the database to ensure they still exist and have the same role
    const result = await sql`
      SELECT id, name, email, role
      FROM users
      WHERE id = ${payload.userId}
    `

    if (result.length === 0) {
      return errorResponse("User not found", 404, "NOT_FOUND")
    }

    const user = snakeToCamel(result[0])

    // Generate new tokens
    const { accessToken, refreshToken } = await generateTokens({
      id: user.id,
      email: user.email,
      role: user.role,
    })

    // Return new tokens
    return successResponse(
      {
        accessToken,
        refreshToken,
      },
      200,
    )
  } catch (error) {
    console.error("Token refresh error:", error)
    return errorResponse("Invalid refresh token", 401, "UNAUTHORIZED")
  }
}
