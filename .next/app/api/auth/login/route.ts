import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { generateTokens } from "@/lib/jwt"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { validateRequest, loginSchema } from "@/lib/validation"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

export async function POST(request: Request) {
  return validateRequest(loginSchema)(request, async (req, validatedData) => {
    try {
      // Find user by email
      const result = await sql`
        SELECT id, name, email, password, role
        FROM users
        WHERE email = ${validatedData.email}
      `

      if (result.length === 0) {
        return errorResponse("Invalid credentials", 401, "UNAUTHORIZED")
      }

      const user = snakeToCamel(result[0])

      // Verify password (in a real app, you'd use bcrypt.compare)
      // For simplicity, we're doing a direct comparison here
      // In production, ALWAYS use proper password hashing
      const isPasswordValid = user.password === validatedData.password

      // For bcrypt, you would use:
      // const isPasswordValid = await bcrypt.compare(validatedData.password, user.password)

      if (!isPasswordValid) {
        return errorResponse("Invalid credentials", 401, "UNAUTHORIZED")
      }

      // Generate tokens
      const { accessToken, refreshToken } = await generateTokens({
        id: user.id,
        email: user.email,
        role: user.role,
      })

      // Return user info and tokens
      return successResponse(
        {
          user: {
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
          },
          accessToken,
          refreshToken,
        },
        200,
      )
    } catch (error) {
      console.error("Login error:", error)
      return errorResponse("Authentication failed", 500)
    }
  })
}
