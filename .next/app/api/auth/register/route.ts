import { sql, snakeToCamel } from "@/lib/db"
import { generateTokens } from "@/lib/jwt"
import { successResponse, errorResponse } from "@/lib/api-response"

export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      return errorResponse("Name, email, and password are required", 400, "INVALID_INPUT")
    }

    // Check if email already exists
    const existingUser = await sql`
      SELECT id FROM users WHERE email = ${body.email}
    `

    if (existingUser.length > 0) {
      return errorResponse("Email already in use", 409, "CONFLICT")
    }

    // In a real app, hash the password before storing
    // const hashedPassword = await bcrypt.hash(body.password, 10)
    // For simplicity, we're storing the password directly
    // In production, ALWAYS hash passwords

    // Insert new user
    const result = await sql`
      INSERT INTO users (name, email, password, role)
      VALUES (${body.name}, ${body.email}, ${body.password}, ${body.role || "user"})
      RETURNING id, name, email, role, created_at, updated_at
    `

    const newUser = snakeToCamel(result[0])

    // Generate tokens
    const { accessToken, refreshToken } = await generateTokens({
      id: newUser.id,
      email: newUser.email,
      role: newUser.role,
    })

    // Return user info and tokens
    return successResponse(
      {
        user: {
          id: newUser.id,
          name: newUser.name,
          email: newUser.email,
          role: newUser.role,
        },
        accessToken,
        refreshToken,
      },
      201,
    )
  } catch (error) {
    console.error("Registration error:", error)
    return errorResponse("Failed to register user", 500)
  }
}
