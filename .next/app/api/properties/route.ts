import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse } from "@/lib/api-response"

// GET all properties
export async function GET() {
  try {
    const result = await sql`
      SELECT id, name, address, latitude, longitude, square_feet, complexity, 
             tags, notes, user_id, created_at, updated_at
      FROM properties
      ORDER BY id
    `

    // Convert snake_case to camelCase
    const properties = result.map((property) => snakeToCamel(property))

    return successResponse({ properties }, 200)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to fetch properties", 500)
  }
}

// POST create a new property
export async function POST(request: Request) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.name || !body.address) {
      return errorResponse("Name and address are required", 400, "INVALID_INPUT")
    }

    // Insert new property
    const result = await sql`
      INSERT INTO properties (
        name, address, latitude, longitude, square_feet, 
        complexity, tags, notes, user_id
      )
      VALUES (
        ${body.name}, 
        ${body.address}, 
        ${body.latitude || null}, 
        ${body.longitude || null}, 
        ${body.squareFeet || null}, 
        ${body.complexity || null}, 
        ${body.tags || null}, 
        ${body.notes || null}, 
        ${body.userId || null}
      )
      RETURNING id, name, address, latitude, longitude, square_feet, 
                complexity, tags, notes, user_id, created_at, updated_at
    `

    const newProperty = snakeToCamel(result[0])

    return successResponse({ property: newProperty }, 201)
  } catch (error) {
    console.error("Database error:", error)
    return errorResponse("Failed to create property", 500)
  }
}
