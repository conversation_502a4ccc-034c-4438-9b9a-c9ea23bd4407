import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"

// GET a specific property by ID
export async function GET(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    const result = await sql`
      SELECT id, name, address, latitude, longitude, square_feet, 
             complexity, tags, notes, user_id, created_at, updated_at
      FROM properties
      WHERE id = ${id}
    `

    if (result.length === 0) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 })
    }

    const property = snakeToCamel(result[0])

    return NextResponse.json({ property }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to fetch property" }, { status: 500 })
  }
}

// PUT update a property
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    const body = await request.json()

    // Check if property exists
    const existingProperty = await sql`
      SELECT id FROM properties WHERE id = ${id}
    `

    if (existingProperty.length === 0) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 })
    }

    // Update property
    const result = await sql`
      UPDATE properties
      SET 
        name = COALESCE(${body.name}, name),
        address = COALESCE(${body.address}, address),
        latitude = COALESCE(${body.latitude}, latitude),
        longitude = COALESCE(${body.longitude}, longitude),
        square_feet = COALESCE(${body.squareFeet}, square_feet),
        complexity = COALESCE(${body.complexity}, complexity),
        tags = COALESCE(${body.tags}, tags),
        notes = COALESCE(${body.notes}, notes),
        user_id = COALESCE(${body.userId}, user_id),
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ${id}
      RETURNING id, name, address, latitude, longitude, square_feet, 
                complexity, tags, notes, user_id, created_at, updated_at
    `

    const updatedProperty = snakeToCamel(result[0])

    return NextResponse.json({ property: updatedProperty }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to update property" }, { status: 500 })
  }
}

// DELETE a property
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  try {
    const id = Number.parseInt(params.id)

    if (isNaN(id)) {
      return NextResponse.json({ error: "Invalid ID format" }, { status: 400 })
    }

    // Check if property exists
    const existingProperty = await sql`
      SELECT id FROM properties WHERE id = ${id}
    `

    if (existingProperty.length === 0) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 })
    }

    // Delete property
    await sql`
      DELETE FROM properties WHERE id = ${id}
    `

    return NextResponse.json({ message: `Property ${id} deleted successfully` }, { status: 200 })
  } catch (error) {
    console.error("Database error:", error)
    return NextResponse.json({ error: "Failed to delete property" }, { status: 500 })
  }
}
