import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { userOrAdmin } from "@/middleware/rbac"
import { validateRequest, createInspectorSchema } from "@/lib/validation"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// GET all inspectors - restricted to users and admins
export async function GET(request: Request) {
  return userOrAdmin(request, async (req, auth) => {
    try {
      // Get query parameters for filtering
      const { searchParams } = new URL(req.url)
      const status = searchParams.get("status")

      // Build the query
      let query = sql`
        SELECT id, name, address, email, phone, points, status, 
               inspection_status, total_inspections, tags, notes, 
               user_id, created_at, updated_at
        FROM inspectors
      `

      // Add filters if provided
      if (status) {
        query = sql`${query} WHERE status = ${status}`
      }

      // Add ordering
      query = sql`${query} ORDER BY id`

      const result = await query

      // Convert snake_case to camelCase
      const inspectors = result.map((inspector) => snakeToCamel(inspector))

      return successResponse({ inspectors }, 200)
    } catch (error) {
      console.error("Database error:", error)
      return errorResponse("Failed to fetch inspectors", 500)
    }
  })
}

// POST create a new inspector - restricted to users and admins with data validation
export async function POST(request: Request) {
  return userOrAdmin(request, async (req, auth) => {
    return validateRequest(createInspectorSchema)(req, async (request, validatedData) => {
      try {
        // Check if email already exists
        const existingInspector = await sql`
          SELECT id FROM inspectors WHERE email = ${validatedData.email}
        `

        if (existingInspector.length > 0) {
          return errorResponse("Email already in use", 409, "CONFLICT")
        }

        // Insert new inspector
        const result = await sql`
          INSERT INTO inspectors (
            name, address, email, phone, points, status, 
            inspection_status, total_inspections, tags, notes, user_id
          )
          VALUES (
            ${validatedData.name}, 
            ${validatedData.address || null}, 
            ${validatedData.email}, 
            ${validatedData.phone || null}, 
            ${validatedData.points || 0}, 
            ${validatedData.status || "active"}, 
            ${validatedData.inspectionStatus || "available"}, 
            ${validatedData.totalInspections || 0}, 
            ${validatedData.tags || null}, 
            ${validatedData.notes || null}, 
            ${validatedData.userId || auth.user.userId} /* Default to current user if not specified */
          )
          RETURNING id, name, address, email, phone, points, status, 
                    inspection_status, total_inspections, tags, notes, 
                    user_id, created_at, updated_at
        `

        const newInspector = snakeToCamel(result[0])

        return successResponse({ inspector: newInspector }, 201)
      } catch (error) {
        console.error("Database error:", error)
        return errorResponse("Failed to create inspector", 500)
      }
    })
  })
}
