import { NextResponse } from "next/server"
import { sql, snakeToCamel } from "@/lib/db"
import { successResponse, errorResponse, CORS_HEADERS } from "@/lib/api-response"
import { userOrAdmin, ownerOrAdmin } from "@/middleware/rbac"
import { validateRequest, updateInspectorSchema } from "@/lib/validation"

// Handle OPTIONS request for CORS preflight
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}

// Helper function to get the user ID associated with an inspector
async function getInspectorUserId(inspectorId: number): Promise<number | null> {
  try {
    const result = await sql`
      SELECT user_id FROM inspectors WHERE id = ${inspectorId}
    `

    if (result.length === 0) {
      return null
    }

    return result[0].user_id
  } catch (error) {
    console.error("Error getting inspector user ID:", error)
    return null
  }
}

// GET a specific inspector by ID - restricted to users and admins
export async function GET(request: Request, { params }: { params: { id: string } }) {
  return userOrAdmin(request, async (req, auth) => {
    try {
      const id = Number.parseInt(params.id)

      if (isNaN(id)) {
        return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
      }

      const result = await sql`
        SELECT id, name, address, email, phone, points, status, 
               inspection_status, total_inspections, tags, notes, 
               user_id, created_at, updated_at
        FROM inspectors
        WHERE id = ${id}
      `

      if (result.length === 0) {
        return errorResponse("Inspector not found", 404, "NOT_FOUND")
      }

      const inspector = snakeToCamel(result[0])

      return successResponse({ inspector }, 200)
    } catch (error) {
      console.error("Database error:", error)
      return errorResponse("Failed to fetch inspector", 500)
    }
  })
}

// PUT update an inspector - restricted to owner or admin
export async function PUT(request: Request, { params }: { params: { id: string } }) {
  const id = Number.parseInt(params.id)

  if (isNaN(id)) {
    return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
  }

  // Get the user ID associated with this inspector
  const getUserId = async () => getInspectorUserId(id)

  return ownerOrAdmin(getUserId)(request, async (req, auth) => {
    return validateRequest(updateInspectorSchema)(req, async (request, validatedData) => {
      try {
        // Check if inspector exists
        const existingInspector = await sql`
          SELECT id FROM inspectors WHERE id = ${id}
        `

        if (existingInspector.length === 0) {
          return errorResponse("Inspector not found", 404, "NOT_FOUND")
        }

        // If email is being updated, check if it's already in use
        if (validatedData.email) {
          const emailCheck = await sql`
            SELECT id FROM inspectors 
            WHERE email = ${validatedData.email} AND id != ${id}
          `

          if (emailCheck.length > 0) {
            return errorResponse("Email already in use by another inspector", 409, "CONFLICT")
          }
        }

        // Update inspector
        const result = await sql`
          UPDATE inspectors
          SET 
            name = COALESCE(${validatedData.name}, name),
            address = COALESCE(${validatedData.address}, address),
            email = COALESCE(${validatedData.email}, email),
            phone = COALESCE(${validatedData.phone}, phone),
            points = COALESCE(${validatedData.points}, points),
            status = COALESCE(${validatedData.status}, status),
            inspection_status = COALESCE(${validatedData.inspectionStatus}, inspection_status),
            total_inspections = COALESCE(${validatedData.totalInspections}, total_inspections),
            tags = COALESCE(${validatedData.tags}, tags),
            notes = COALESCE(${validatedData.notes}, notes),
            updated_at = CURRENT_TIMESTAMP
          WHERE id = ${id}
          RETURNING id, name, address, email, phone, points, status, 
                    inspection_status, total_inspections, tags, notes, 
                    user_id, created_at, updated_at
        `

        const updatedInspector = snakeToCamel(result[0])

        return successResponse({ inspector: updatedInspector }, 200)
      } catch (error) {
        console.error("Database error:", error)
        return errorResponse("Failed to update inspector", 500)
      }
    })
  })
}

// DELETE an inspector - restricted to admin only
export async function DELETE(request: Request, { params }: { params: { id: string } }) {
  const id = Number.parseInt(params.id)

  if (isNaN(id)) {
    return errorResponse("Invalid ID format", 400, "INVALID_INPUT")
  }

  // Only admins can delete inspectors
  return ownerOrAdmin(async () => getInspectorUserId(id))(request, async (req, auth) => {
    try {
      // Check if inspector exists
      const existingInspector = await sql`
        SELECT id FROM inspectors WHERE id = ${id}
      `

      if (existingInspector.length === 0) {
        return errorResponse("Inspector not found", 404, "NOT_FOUND")
      }

      // Delete inspector
      await sql`
        DELETE FROM inspectors WHERE id = ${id}
      `

      return successResponse({ message: `Inspector ${id} deleted successfully` }, 200)
    } catch (error) {
      console.error("Database error:", error)
      return errorResponse("Failed to delete inspector", 500)
    }
  })
}
