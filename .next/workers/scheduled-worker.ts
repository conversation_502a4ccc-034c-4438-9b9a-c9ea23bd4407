import { process<PERSON>ronJobs } from "./cronjob-worker"

/**
 * This function is designed to be called by a scheduled job
 * It can be used with Vercel Cron Jobs, AWS Lambda, or any other scheduler
 */
export async function runScheduledWorker() {
  console.log("Running scheduled worker at", new Date().toISOString())

  try {
    // Process cronjobs
    const result = await processCronJobs()

    console.log("Scheduled worker completed:", result)
    return { success: true, result }
  } catch (error) {
    console.error("Error in scheduled worker:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}

// If this file is executed directly (e.g., with node workers/scheduled-worker.js)
// Run the worker immediately
if (require.main === module) {
  runScheduledWorker()
    .then((result) => {
      console.log("Worker execution result:", result)
      process.exit(0)
    })
    .catch((error) => {
      console.error("Worker execution failed:", error)
      process.exit(1)
    })
}
