import { sql } from "@/lib/db"
import { sendEmail } from "@/lib/email"
import crypto from "crypto"

/**
 * Process pending cronjobs
 */
export async function processCronJobs() {
  console.log("Starting cronjob processing...")

  try {
    // Check if there are any pending jobs
    const pendingCount = await sql`
      SELECT COUNT(*) FROM cronjobs 
      WHERE status = 'pending' 
      AND (next_retry_at IS NULL OR next_retry_at <= CURRENT_TIMESTAMP)
    `

    const count = Number.parseInt(pendingCount[0].count)

    if (count === 0) {
      console.log("No pending jobs found, sending notification...")
      await sendNoJobsNotification()
      return { processed: 0, success: 0, failed: 0 }
    }

    // Get the next batch of jobs to process (limit to 10 at a time)
    const jobs = await sql`
      SELECT * FROM cronjobs 
      WHERE status = 'pending' 
      AND (next_retry_at IS NULL OR next_retry_at <= CURRENT_TIMESTAMP)
      ORDER BY priority DESC, created_at ASC
      LIMIT 10
    `

    console.log(`Processing ${jobs.length} jobs...`)

    let success = 0
    let failed = 0

    // Process each job
    for (const job of jobs) {
      try {
        // Mark job as processing
        await sql`
          UPDATE cronjobs 
          SET status = 'processing', 
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ${job.id}
        `

        // Process the job based on provider type
        let result
        if (job.provider_type === "email") {
          result = await processEmailJob(job)
        } else if (job.provider_type === "sms") {
          result = await processSmsJob(job)
        } else {
          throw new Error(`Unsupported provider type: ${job.provider_type}`)
        }

        if (result.success) {
          // Mark job as sent
          await sql`
            UPDATE cronjobs 
            SET status = 'sent', 
                sent_date = CURRENT_TIMESTAMP,
                last_result = ${JSON.stringify(result)},
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ${job.id}
          `
          success++
        } else {
          // Mark job as failed and schedule retry if possible
          const retryTimes = job.retry_times + 1
          const maxRetries = job.max_retries

          if (retryTimes < maxRetries) {
            // Calculate next retry time (exponential backoff: 5min, 15min, 45min, etc.)
            const backoffMinutes = Math.pow(3, retryTimes) * 5
            const nextRetry = new Date()
            nextRetry.setMinutes(nextRetry.getMinutes() + backoffMinutes)

            await sql`
              UPDATE cronjobs 
              SET status = 'pending', 
                  retry_times = ${retryTimes},
                  next_retry_at = ${nextRetry.toISOString()},
                  last_result = ${JSON.stringify(result)},
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = ${job.id}
            `
          } else {
            // Max retries reached, mark as permanently failed
            await sql`
              UPDATE cronjobs 
              SET status = 'failed', 
                  last_result = ${JSON.stringify(result)},
                  updated_at = CURRENT_TIMESTAMP
              WHERE id = ${job.id}
            `
          }
          failed++
        }
      } catch (error) {
        console.error(`Error processing job ${job.id}:`, error)

        // Mark job as failed
        await sql`
          UPDATE cronjobs 
          SET status = 'failed', 
              last_result = ${JSON.stringify({
                success: false,
                error: error instanceof Error ? error.message : "Unknown error",
              })},
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ${job.id}
        `
        failed++
      }
    }

    console.log(`Processed ${jobs.length} jobs: ${success} succeeded, ${failed} failed`)
    return { processed: jobs.length, success, failed }
  } catch (error) {
    console.error("Error in cronjob processing:", error)
    return { processed: 0, success: 0, failed: 0, error }
  }
}

/**
 * Process an email job
 */
async function processEmailJob(job: any) {
  try {
    const payload = job.payload

    // If there's a template ID, get the template
    let emailContent = ""
    let emailSubject = job.subject || "Notification"

    if (job.template_id) {
      const template = await sql`
        SELECT * FROM templates 
        WHERE id = ${job.template_id} 
        AND type = 'email'
        AND status = 'active'
      `

      if (template.length > 0) {
        // Process the template with variables from payload
        emailContent = template[0].value

        // Replace variables in the template
        Object.entries(payload).forEach(([key, value]) => {
          const regex = new RegExp(`{{${key}}}`, "g")
          emailContent = emailContent.replace(regex, value as string)
        })
      }
    } else if (payload.content) {
      // Use content directly from payload
      emailContent = payload.content as string
    } else {
      throw new Error("No email content or template provided")
    }

    // If subject is in payload, use it
    if (payload.subject) {
      emailSubject = payload.subject as string
    }

    // Send the email
    const result = await sendEmail({
      to: job.recipient,
      subject: emailSubject,
      html: emailContent,
    })

    return result
  } catch (error) {
    console.error("Error processing email job:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}

/**
 * Process an SMS job (placeholder for future implementation)
 */
async function processSmsJob(job: any) {
  // This is a placeholder for SMS processing
  // In a real implementation, you would integrate with an SMS provider
  console.log(`Would send SMS to ${job.recipient} with payload:`, job.payload)

  // For now, we'll simulate success
  return { success: true, messageId: `sms-${crypto.randomUUID()}` }
}

/**
 * Send a notification when no jobs are found
 */
async function sendNoJobsNotification() {
  try {
    const result = await sendEmail({
      to: "<EMAIL>",
      subject: "CronJob Worker Notification",
      html: `
        <h1>CronJob Worker Notification</h1>
        <p>The cronjob worker ran at ${new Date().toISOString()} and found no pending jobs in the queue.</p>
        <p>This is an automated notification.</p>
      `,
    })

    console.log("No jobs notification sent:", result)
    return result
  } catch (error) {
    console.error("Error sending no jobs notification:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    }
  }
}
