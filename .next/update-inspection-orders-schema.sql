-- Update inspection_orders table with new fields
ALTER TABLE inspection_orders
ADD COLUMN IF NOT EXISTS client_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS client_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS client_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS property_address TEXT,
ADD COLUMN IF NOT EXISTS property_md5 VARCHAR(32),
ADD COLUMN IF NOT EXISTS property_type VARCHAR(50) CHECK (property_type IN ('single', 'family', 'duplex', 'triplex', 'quadplex', 'commercial', 'apartment')),
ADD COLUMN IF NOT EXISTS year_built INTEGER,
ADD COLUMN IF NOT EXISTS foundation_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS gate_code VARCHAR(50),
ADD COLUMN IF NOT EXISTS lockbox_code VARCHAR(50),
ADD COLUMN IF NOT EXISTS mls_number VARCHAR(100),
ADD COLUMN IF NOT EXISTS property_tags TEXT[],
ADD COLUMN IF NOT EXISTS add_ons JSONB,
ADD COLUMN IF NOT EXISTS agent_name VARCHAR(255),
ADD COLUMN IF NOT EXISTS agent_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS agent_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS agent_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS inspection_fees DECIMAL(10, 2),
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'inprogress', 'completed', 'scheduled', 'paid', 'cancelled', 'inspected', 'reportsent')),
ADD COLUMN IF NOT EXISTS created_by INTEGER REFERENCES users(id),
ADD COLUMN IF NOT EXISTS updated_by INTEGER REFERENCES users(id),
ADD COLUMN IF NOT EXISTS assigned_inspector_ids INTEGER[];

-- Update existing columns if needed
ALTER TABLE inspection_orders
ALTER COLUMN created_at SET DEFAULT CURRENT_TIMESTAMP,
ALTER COLUMN updated_at SET DEFAULT CURRENT_TIMESTAMP;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_inspection_orders_status ON inspection_orders(status);
CREATE INDEX IF NOT EXISTS idx_inspection_orders_client_email ON inspection_orders(client_email);
CREATE INDEX IF NOT EXISTS idx_inspection_orders_property_md5 ON inspection_orders(property_md5);
