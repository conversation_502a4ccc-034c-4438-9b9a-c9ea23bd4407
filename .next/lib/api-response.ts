import { NextResponse } from "next/server"

// Error code types
export type ErrorCode =
  | "INVALID_INPUT"
  | "NOT_FOUND"
  | "UNAUTHORIZED"
  | "FORBIDDEN"
  | "CONFLICT"
  | "SERVER_ERROR"
  | "VALIDATION_ERROR"
  | null

// Standard API response type
export interface ApiResponse<T = any> {
  success: boolean
  statusCode: number
  errorCode: ErrorCode
  error: string | null
  data: T | null
}

// CORS headers to allow all origins
export const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*", // Allow any origin
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Max-Age": "86400", // 24 hours cache for preflight requests
}

/**
 * Create a successful API response
 */
export function successResponse<T>(data: T, statusCode: 200 | 201 = 200): NextResponse<ApiResponse<T>> {
  return NextResponse.json(
    {
      success: true,
      statusCode,
      errorCode: null,
      error: null,
      data,
    },
    {
      status: statusCode,
      headers: CORS_HEADERS,
    },
  )
}

/**
 * Create an error API response
 */
export function errorResponse(
  message: string,
  statusCode = 500,
  errorCode: ErrorCode = "SERVER_ERROR",
): NextResponse<ApiResponse<null>> {
  return NextResponse.json(
    {
      success: false,
      statusCode,
      errorCode,
      error: message,
      data: null,
    },
    {
      status: statusCode,
      headers: CORS_HEADERS,
    },
  )
}

/**
 * Map common error scenarios to appropriate error codes
 */
export function getErrorCodeFromStatus(statusCode: number): ErrorCode {
  switch (statusCode) {
    case 400:
      return "INVALID_INPUT"
    case 401:
      return "UNAUTHORIZED"
    case 403:
      return "FORBIDDEN"
    case 404:
      return "NOT_FOUND"
    case 409:
      return "CONFLICT"
    case 422:
      return "VALIDATION_ERROR"
    default:
      return "SERVER_ERROR"
  }
}
