import { NextResponse } from "next/server"

// CORS headers to allow all origins
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*", // Allow any origin
  "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With",
  "Access-Control-Max-Age": "86400", // 24 hours cache for preflight requests
}

/**
 * Add CORS headers to a response
 */
export function addCorsHeaders<T>(response: NextResponse<T>): NextResponse<T> {
  // Add CORS headers to the response
  Object.entries(CORS_HEADERS).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  return response
}

/**
 * Handle OPTIONS preflight requests
 */
export function handleCorsOptions(): NextResponse {
  return new NextResponse(null, {
    status: 200,
    headers: CORS_HEADERS,
  })
}
