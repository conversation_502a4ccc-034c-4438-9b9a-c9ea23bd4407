import nodemailer from "nodemailer"
import { sql } from "./db"

// Create a transporter using SMTP settings from the database
async function createTransporter() {
  try {
    // Get email settings from the database
    const settings = await sql`
      SELECT name, value FROM settings 
      WHERE type = 'email' 
      AND name IN ('email_smtp_host', 'email_smtp_port', 'email_username', 'email_password', 'email_from_name')
    `

    // Convert to a settings object
    const emailConfig: Record<string, string> = {}
    settings.forEach((setting) => {
      emailConfig[setting.name] = setting.value
    })

    // Log the email configuration (without password)
    console.log("Email configuration:", {
      host: emailConfig.email_smtp_host || process.env.EMAIL_SMTP_HOST,
      port: Number.parseInt(emailConfig.email_smtp_port || process.env.EMAIL_SMTP_PORT || "587"),
      secure: false,
      auth: {
        user: emailConfig.email_username || process.env.EMAIL_USERNAME,
      },
    })

    // Create a transporter
    const transporter = nodemailer.createTransport({
      host: emailConfig.email_smtp_host || process.env.EMAIL_SMTP_HOST,
      port: Number.parseInt(emailConfig.email_smtp_port || process.env.EMAIL_SMTP_PORT || "587"),
      secure: false, // true for 465, false for other ports
      auth: {
        user: emailConfig.email_username || process.env.EMAIL_USERNAME,
        pass: emailConfig.email_password || process.env.EMAIL_PASSWORD,
      },
    })

    // Verify the connection
    await transporter.verify()
    console.log("Email transporter verified successfully")

    return transporter
  } catch (error) {
    console.error("Error creating email transporter:", error)

    // Fallback to environment variables if database settings are not available
    console.log("Falling back to environment variables for email configuration")

    try {
      const transporter = nodemailer.createTransport({
        host: process.env.EMAIL_SMTP_HOST,
        port: Number.parseInt(process.env.EMAIL_SMTP_PORT || "587"),
        secure: false,
        auth: {
          user: process.env.EMAIL_USERNAME,
          pass: process.env.EMAIL_PASSWORD,
        },
      })

      // Verify the fallback connection
      await transporter.verify()
      console.log("Fallback email transporter verified successfully")

      return transporter
    } catch (fallbackError) {
      console.error("Error creating fallback email transporter:", fallbackError)
      throw fallbackError
    }
  }
}

/**
 * Send an email
 */
export async function sendEmail({
  to,
  subject,
  html,
  text,
  from,
}: {
  to: string
  subject: string
  html?: string
  text?: string
  from?: string
}) {
  try {
    const transporter = await createTransporter()

    const fromName = from || (await getFromName())

    console.log(`Sending email to ${to} with subject "${subject}"`)

    const info = await transporter.sendMail({
      from: fromName,
      to,
      subject,
      text,
      html,
    })

    console.log("Email sent:", info.messageId)
    return { success: true, messageId: info.messageId }
  } catch (error) {
    console.error("Error sending email:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      details: error,
    }
  }
}

/**
 * Get the from name from settings or environment variables
 */
async function getFromName(): Promise<string> {
  try {
    const result = await sql`
      SELECT value FROM settings 
      WHERE type = 'email' AND name = 'email_from_name'
      LIMIT 1
    `

    if (result.length > 0) {
      return result[0].value
    }

    return process.env.EMAIL_FROM_NAME || "Inspection System"
  } catch (error) {
    return process.env.EMAIL_FROM_NAME || "Inspection System"
  }
}

/**
 * Process an email template with variables
 */
export function processTemplate(template: string, variables: Record<string, string>): string {
  let processed = template

  // Replace all variables in the format {{variable_name}}
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, "g")
    processed = processed.replace(regex, value)
  })

  return processed
}
