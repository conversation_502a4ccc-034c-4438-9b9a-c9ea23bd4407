import { z } from "zod"
import { errorResponse } from "./api-response"
import type { NextResponse } from "next/server"

/**
 * Validate request body against a Zod schema
 * @param schema Zod schema to validate against
 */
export function validateRequest<T>(schema: z.ZodType<T>) {
  return async function validate(
    request: Request,
    handler: (request: Request, validatedData: T) => Promise<NextResponse>,
  ) {
    try {
      // Parse the request body
      const body = await request.json()

      // Validate against the schema
      const result = schema.safeParse(body)

      // If validation fails, return error response
      if (!result.success) {
        const errors = result.error.errors.map((err) => ({
          path: err.path.join("."),
          message: err.message,
        }))

        return errorResponse("Validation failed", 400, "VALIDATION_ERROR")
      }

      // Validation succeeded, proceed with handler
      return handler(request, result.data)
    } catch (error) {
      console.error("Validation error:", error)
      return errorResponse("Invalid request body", 400, "INVALID_INPUT")
    }
  }
}

// Common validation schemas
export const idParamSchema = z.object({
  id: z.string().refine((val) => !isNaN(Number.parseInt(val)), {
    message: "ID must be a valid number",
  }),
})

// Inspector schemas
export const createInspectorSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  address: z.string().optional(),
  phone: z.string().optional(),
  points: z.number().optional(),
  status: z.enum(["active", "inactive"]).optional(),
  inspectionStatus: z.enum(["available", "busy", "on_leave"]).optional(),
  totalInspections: z.number().optional(),
  tags: z.array(z.string()).optional(),
  notes: z.string().optional(),
  userId: z.number().optional(),
})

export const updateInspectorSchema = createInspectorSchema.partial()

// User schemas
export const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  role: z.enum(["user", "admin", "inspector"]).optional(),
})

export const updateUserSchema = createUserSchema.partial()

// Login schema
export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
})

// Order schemas
export const createOrderSchema = z.object({
  // Client information
  clientName: z.string().min(1, "Client name is required"),
  clientPhone: z.string().optional(),
  clientEmail: z.string().email("Invalid client email").optional(),

  // Property information
  propertyAddress: z.string().min(1, "Property address is required"),
  propertyMD5: z.string().optional(),
  propertyType: z.enum(["single", "family", "duplex", "triplex", "quadplex", "commercial", "apartment"]).optional(),
  yearBuilt: z.number().int().positive().optional(),
  foundationType: z.string().optional(),
  gateCode: z.string().optional(),
  lockboxCode: z.string().optional(),
  mlsNumber: z.string().optional(),
  propertyTags: z.array(z.string()).optional(),
  addOns: z.record(z.any()).optional(),

  // Agent information
  agentName: z.string().optional(),
  agentEmail: z.string().email("Invalid agent email").optional(),
  agentPhone: z.string().optional(),
  agentType: z.string().optional(),

  // Order information
  inspectionFees: z.number().nonnegative().optional(),
  status: z
    .enum(["pending", "inprogress", "completed", "scheduled", "paid", "cancelled", "inspected", "reportsent"])
    .default("pending"),
  inspectionDate: z.string().optional(),
  assignedInspectorIds: z.array(z.number()).optional(),

  // Legacy fields for compatibility
  propertyId: z.number().optional(),
  userId: z.number().optional(),
  inspectionOrderId: z.string().optional(),
})

export const updateOrderSchema = createOrderSchema.partial()

export const changeOrderStatusSchema = z.object({
  status: z.enum(["pending", "inprogress", "completed", "scheduled", "paid", "cancelled", "inspected", "reportsent"]),
  notes: z.string().optional(),
})
