import { OpenAPIRegistry } from "@asteasolutions/zod-to-openapi"
import { createDocument } from "@asteasolutions/zod-to-openapi"
import { z } from "zod"

// Create a registry to hold all our API schemas and routes
const registry = new OpenAPIRegistry()

// Define basic schemas
const ErrorResponse = z.object({
  success: z.literal(false),
  statusCode: z.number(),
  errorCode: z.string().nullable(),
  error: z.string(),
  data: z.null(),
})

const SuccessResponse = z.object({
  success: z.literal(true),
  statusCode: z.number(),
  errorCode: z.null(),
  error: z.null(),
  data: z.any(),
})

// Register basic schemas
registry.register("ErrorResponse", ErrorResponse)
registry.register("SuccessResponse", SuccessResponse)

// Define a simple path for testing
registry.registerPath({
  method: "get",
  path: "/api/users",
  tags: ["Users"],
  responses: {
    200: {
      description: "List of users",
      content: {
        "application/json": {
          schema: SuccessResponse,
        },
      },
    },
    401: {
      description: "Unauthorized",
      content: {
        "application/json": {
          schema: ErrorResponse,
        },
      },
    },
  },
})

// Generate the OpenAPI document
export function generateOpenApiDocument() {
  try {
    return createDocument(
      {
        openapi: "3.0.0",
        info: {
          title: "Inspection API",
          version: "1.0.0",
          description: "API for managing property inspections",
        },
        servers: [
          {
            url: "/",
            description: "Current server",
          },
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: "http",
              scheme: "bearer",
              bearerFormat: "JWT",
            },
          },
        },
      },
      registry.definitions,
    )
  } catch (error) {
    console.error("Error generating OpenAPI document:", error)
    // Return a minimal valid OpenAPI document
    return {
      openapi: "3.0.0",
      info: {
        title: "Inspection API (Error)",
        version: "1.0.0",
        description: "Error generating full API documentation",
      },
      paths: {},
    }
  }
}
