import { SignJWT, jwtVerify } from "jose"

// Secret key for JWT signing and verification
const JWT_SECRET = new TextEncoder().encode(process.env.JWT_SECRET || "your-secret-key-at-least-32-chars-long")

// Token expiration times
export const ACCESS_TOKEN_EXPIRES_IN = "15m" // 15 minutes
export const REFRESH_TOKEN_EXPIRES_IN = "7d" // 7 days

// User payload type
export interface JWTPayload {
  userId: number
  email: string
  role: string
  iat?: number
  exp?: number
}

/**
 * Generate a JWT token
 */
export async function signJWT(payload: JWTPayload, expiresIn: string): Promise<string> {
  try {
    const token = await new SignJWT({ ...payload })
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt()
      .setExpirationTime(expiresIn)
      .sign(JWT_SECRET)

    return token
  } catch (error) {
    console.error("Error signing JWT:", error)
    throw new Error("Failed to sign JWT")
  }
}

/**
 * Verify a JWT token
 */
export async function verifyJWT(token: string): Promise<JWTPayload> {
  try {
    const { payload } = await jwtVerify(token, JWT_SECRET)
    return payload as JWTPayload
  } catch (error) {
    console.error("Error verifying JWT:", error)
    throw new Error("Invalid token")
  }
}

/**
 * Generate both access and refresh tokens
 */
export async function generateTokens(user: { id: number; email: string; role: string }) {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    role: user.role,
  }

  const accessToken = await signJWT(payload, ACCESS_TOKEN_EXPIRES_IN)
  const refreshToken = await signJWT(payload, REFRESH_TOKEN_EXPIRES_IN)

  return { accessToken, refreshToken }
}
