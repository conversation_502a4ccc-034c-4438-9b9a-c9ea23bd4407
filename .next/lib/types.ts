// User types
export interface User {
  id: number
  name: string
  email: string
  role: string
}

// Inspector types
export interface Inspector {
  id: number
  name: string
  specialization: string
  rating: number
}

// Property types
export interface Property {
  id: number
  address: string
  city: string
  state: string
  type: string
}

// Inspection Order types
export interface InspectionOrder {
  id: number
  propertyId: number
  inspectorId: number
  userId: number
  status: "pending" | "scheduled" | "completed" | "cancelled"
  scheduledDate: string
  type: string
}

// Schedule types
export interface TimeSlot {
  start: string
  end: string
  available: boolean
  inspectionOrderId?: number
}

export interface Schedule {
  id: number
  inspectorId: number
  date: string
  timeSlots: TimeSlot[]
}
