-- Seed data for custom_fields table
INSERT INTO custom_fields (name, type, control_type, default_value, status, created_by)
VALUES
  ('inspection_date', 'date', 'datepicker', NULL, 'active', 1),
  ('square_footage', 'number', 'number_input', '0', 'active', 1),
  ('property_age', 'number', 'number_input', '0', 'active', 1),
  ('has_pool', 'boolean', 'checkbox', 'false', 'active', 1),
  ('roof_type', 'string', 'dropdown', 'Asphalt Shingle', 'active', 1),
  ('foundation_type', 'string', 'dropdown', 'Concrete Slab', 'active', 1),
  ('hvac_system_type', 'string', 'dropdown', 'Central', 'active', 1),
  ('electrical_system', 'string', 'dropdown', '200 Amp', 'active', 1),
  ('plumbing_material', 'string', 'dropdown', 'Copper', 'active', 1),
  ('notes', 'text', 'textarea', '', 'active', 1),
  ('inspector_rating', 'number', 'rating', '5', 'active', 1),
  ('client_email', 'email', 'email_input', '', 'active', 1),
  ('client_phone', 'phone', 'phone_input', '', 'active', 1),
  ('inspection_fee', 'currency', 'currency_input', '0', 'active', 1),
  ('special_instructions', 'text', 'textarea', '', 'active', 1);

-- Seed data for templates table
INSERT INTO templates (name, type, status, value, custom_fields, custom_values, created_by)
VALUES
  (
    'Standard Home Inspection', 
    'inspection_report', 
    'active', 
    'This is a standard home inspection template covering all major systems and components.',
    '[
      "inspection_date",
      "square_footage",
      "property_age",
      "has_pool",
      "roof_type",
      "foundation_type",
      "hvac_system_type",
      "electrical_system",
      "plumbing_material",
      "notes"
    ]',
    '{
      "sections": [
        {
          "name": "Exterior",
          "fields": ["roof_type", "foundation_type"]
        },
        {
          "name": "Interior",
          "fields": ["square_footage", "notes"]
        },
        {
          "name": "Systems",
          "fields": ["hvac_system_type", "electrical_system", "plumbing_material"]
        }
      ]
    }',
    1
  ),
  (
    'Pool Inspection', 
    'inspection_report', 
    'active', 
    'Specialized inspection template for properties with swimming pools.',
    '[
      "inspection_date",
      "has_pool",
      "notes"
    ]',
    '{
      "sections": [
        {
          "name": "Pool Features",
          "fields": ["has_pool", "notes"]
        }
      ]
    }',
    1
  ),
  (
    'Commercial Property', 
    'inspection_report', 
    'active', 
    'Template for commercial property inspections.',
    '[
      "inspection_date",
      "square_footage",
      "property_age",
      "hvac_system_type",
      "electrical_system",
      "notes"
    ]',
    '{
      "sections": [
        {
          "name": "Property Details",
          "fields": ["square_footage", "property_age"]
        },
        {
          "name": "Systems",
          "fields": ["hvac_system_type", "electrical_system"]
        },
        {
          "name": "Additional Notes",
          "fields": ["notes"]
        }
      ]
    }',
    1
  ),
  (
    'New Construction', 
    'inspection_report', 
    'active', 
    'Template for new construction inspections.',
    '[
      "inspection_date",
      "square_footage",
      "roof_type",
      "foundation_type",
      "hvac_system_type",
      "electrical_system",
      "plumbing_material",
      "notes"
    ]',
    '{
      "sections": [
        {
          "name": "Construction Details",
          "fields": ["square_footage", "roof_type", "foundation_type"]
        },
        {
          "name": "Systems",
          "fields": ["hvac_system_type", "electrical_system", "plumbing_material"]
        },
        {
          "name": "Additional Notes",
          "fields": ["notes"]
        }
      ]
    }',
    1
  ),
  (
    'Email Notification', 
    'email', 
    'active', 
    'Dear {{client_name}},\n\nYour inspection has been scheduled for {{inspection_date}} at {{property_address}}.\n\nThank you,\nInspection Team',
    '[
      "client_email",
      "inspection_date",
      "special_instructions"
    ]',
    '{
      "variables": ["client_name", "inspection_date", "property_address"]
    }',
    1
  );

-- Seed data for settings table
INSERT INTO settings (name, type, value, created_by)
VALUES
  ('company_name', 'general', 'ABC Inspection Services', 1),
  ('company_email', 'general', '<EMAIL>', 1),
  ('company_phone', 'general', '************', 1),
  ('company_address', 'general', '123 Main St, Austin, TX 78701', 1),
  ('company_logo', 'general', 'https://example.com/logo.png', 1),
  ('default_inspection_fee', 'financial', '450', 1),
  ('tax_rate', 'financial', '8.25', 1),
  ('payment_methods', 'financial', 'Credit Card, Cash, Check', 1),
  ('email_smtp_host', 'email', 'smtp.example.com', 1),
  ('email_smtp_port', 'email', '587', 1),
  ('email_username', 'email', '<EMAIL>', 1),
  ('email_password', 'email', 'password123', 1),
  ('email_from_name', 'email', 'ABC Inspections', 1),
  ('notification_new_order', 'notifications', 'true', 1),
  ('notification_order_status_change', 'notifications', 'true', 1),
  ('notification_payment_received', 'notifications', 'true', 1);
