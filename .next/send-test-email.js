// <PERSON>ript to send a test email using the API

async function sendTestEmail() {
  try {
    // First, we need to get an access token (you would normally log in to get this)
    // For this example, we'll assume you already have a token
    // You can get a token by logging in through the UI and copying it from localStorage

    // Replace this with your actual token
    const token = "YOUR_ACCESS_TOKEN_HERE"

    // Prepare the email data
    const emailData = {
      to: "<EMAIL>",
      subject: "Test Email from API Script",
      html: `
        <h1>Test Email</h1>
        <p>This is a test email sent from the API script at ${new Date().toISOString()}</p>
        <p>If you received this email, the email service is working correctly.</p>
      `,
    }

    console.log("Sending test <NAME_EMAIL>...")

    // Send the request to the API
    const response = await fetch("http://localhost:3000/api/email/test", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(emailData),
    })

    const result = await response.json()

    if (result.success) {
      console.log("Email sent successfully!")
      console.log("Result:", result.data)
    } else {
      console.error("Failed to send email:", result.error)
    }
  } catch (error) {
    console.error("Error sending test email:", error)
  }
}

// Execute the function
sendTestEmail()

// Instructions for use:
// 1. Replace YOUR_ACCESS_TOKEN_HERE with a valid JWT token
// 2. Run this script with Node.js
// 3. Check the console for results
// 4. Check the recipient's inbox for the test email
console.log("Note: You need to replace the placeholder token with a valid JWT token before running this script.")
