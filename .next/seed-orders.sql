-- Insert sample orders with the new schema
INSERT INTO inspection_orders (
  inspection_order_id, client_name, client_phone, client_email,
  property_address, property_md5, property_type, year_built,
  foundation_type, gate_code, lockbox_code, mls_number,
  property_tags, add_ons, agent_name, agent_email,
  agent_phone, agent_type, inspection_fees, status,
  inspection_date, user_id, created_by
)
VALUES
  (
    'INS-2023-1001', 
    '<PERSON>', 
    '************', 
    '<EMAIL>',
    '123 Main St, Austin, TX 78701', 
    md5('123 Main St, Austin, TX 78701'), 
    'single', 
    1998,
    'Slab', 
    '1234', 
    '5678', 
    'MLS12345',
    ARRAY['new construction', 'pool'], 
    '{"pool_inspection": true, "termite_inspection": true}', 
    '<PERSON>tor', 
    '<EMAIL>',
    '************', 
    'seller agent', 
    450.00, 
    'scheduled',
    '2023-12-15', 
    1, 
    1
  ),
  (
    'INS-2023-1002', 
    '<PERSON>', 
    '************', 
    '<EMAIL>',
    '456 Oak Ave, Austin, TX 78704', 
    md5('456 Oak Ave, Austin, TX 78704'), 
    'duplex', 
    1975,
    'Pier and Beam', 
    NULL, 
    '1122', 
    'MLS54321',
    ARRAY['renovation', 'historic'], 
    '{"radon_testing": true}', 
    'Mike Agent', 
    '<EMAIL>',
    '************', 
    'buyer agent', 
    550.00, 
    'pending',
    '2023-12-20', 
    1, 
    1
  ),
  (
    'INS-2023-1003', 
    'Robert Williams', 
    '555-666-7777', 
    '<EMAIL>',
    '789 Pine Blvd, Austin, TX 78745', 
    md5('789 Pine Blvd, Austin, TX 78745'), 
    'commercial', 
    2005,
    'Concrete', 
    '4455', 
    '6677', 
    'MLS98765',
    ARRAY['commercial', 'office'], 
    '{"commercial_inspection": true, "hvac_inspection": true}', 
    'Lisa Broker', 
    '<EMAIL>',
    '555-888-9999', 
    'commercial agent', 
    1200.00, 
    'completed',
    '2023-12-01', 
    2, 
    2
  ),
  (
    'INS-2023-1004', 
    'Emily Davis', 
    '555-111-2222', 
    '<EMAIL>',
    '101 Cedar Ln, Austin, TX 78702', 
    md5('101 Cedar Ln, Austin, TX 78702'), 
    'family', 
    2015,
    'Slab', 
    '8899', 
    NULL, 
    'MLS45678',
    ARRAY['new construction'], 
    '{"pool_inspection": false, "termite_inspection": true}', 
    'David Realtor', 
    '<EMAIL>',
    '555-333-4444', 
    'seller agent', 
    400.00, 
    'paid',
    '2023-11-25', 
    1, 
    1
  ),
  (
    'INS-2023-1005', 
    'Michael Brown', 
    '555-555-5555', 
    '<EMAIL>',
    '222 Maple Dr, Austin, TX 78723', 
    md5('222 Maple Dr, Austin, TX 78723'), 
    'apartment', 
    1990,
    'Concrete', 
    '7788', 
    '9900', 
    'MLS78901',
    ARRAY['condo', 'urban'], 
    '{"mold_inspection": true}', 
    'Susan Agent', 
    '<EMAIL>',
    '555-777-8888', 
    'buyer agent', 
    350.00, 
    'inprogress',
    '2023-12-10', 
    2, 
    2
  );

-- Create order status history entries
INSERT INTO order_status_history (
  order_id, previous_status, new_status, changed_by, notes
)
VALUES
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1001'),
    'pending',
    'scheduled',
    1,
    'Scheduled for Dec 15th with inspector approval'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1003'),
    'pending',
    'scheduled',
    2,
    'Scheduled for Dec 1st'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1003'),
    'scheduled',
    'inprogress',
    2,
    'Inspector on site'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1003'),
    'inprogress',
    'completed',
    2,
    'Inspection completed, report being prepared'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1004'),
    'pending',
    'scheduled',
    1,
    'Scheduled for Nov 25th'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1004'),
    'scheduled',
    'completed',
    1,
    'Inspection completed'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1004'),
    'completed',
    'paid',
    1,
    'Payment received'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1005'),
    'pending',
    'scheduled',
    2,
    'Scheduled for Dec 10th'
  ),
  (
    (SELECT id FROM inspection_orders WHERE inspection_order_id = 'INS-2023-1005'),
    'scheduled',
    'inprogress',
    2,
    'Inspector on site'
  );
