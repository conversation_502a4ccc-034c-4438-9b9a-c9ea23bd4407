-- Create enhanced schedules table with validation constraints
CREATE TABLE IF NOT EXISTS enhanced_schedules (
  id SERIAL PRIMARY KEY,
  inspector_id INTEGER NOT NULL REFERENCES inspectors(id),
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  available BOOLEAN NOT NULL DEFAULT true,
  inspection_order_id INTEGER REFERENCES inspection_orders(id),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  -- Ensure end_time is after start_time
  CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_enhanced_schedules_inspector_date ON enhanced_schedules(inspector_id, date);
CREATE INDEX IF NOT EXISTS idx_enhanced_schedules_order_id ON enhanced_schedules(inspection_order_id);

-- Create a function to check scheduling constraints
CREATE OR REPLACE FUNCTION check_inspector_schedule_constraints()
RETURNS TRIGGER AS $$
DECLARE
  daily_count INTEGER;
  overlapping_count INTEGER;
BEGIN
  -- Check if inspector already has 3 assignments for the day
  SELECT COUNT(*) INTO daily_count
  FROM enhanced_schedules
  WHERE inspector_id = NEW.inspector_id 
    AND date = NEW.date 
    AND inspection_order_id IS NOT NULL
    AND id != COALESCE(NEW.id, 0);
    
  IF daily_count >= 3 THEN
    RAISE EXCEPTION 'Inspector cannot have more than 3 assignments per day';
  END IF;
  
  -- Check for time overlaps
  SELECT COUNT(*) INTO overlapping_count
  FROM enhanced_schedules
  WHERE inspector_id = NEW.inspector_id 
    AND date = NEW.date
    AND id != COALESCE(NEW.id, 0)
    AND (
      (NEW.start_time < end_time AND NEW.end_time > start_time) -- New schedule overlaps with existing
    );
    
  IF overlapping_count > 0 THEN
    RAISE EXCEPTION 'Schedule overlaps with existing assignments for this inspector';
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to enforce constraints
CREATE TRIGGER enforce_inspector_schedule_constraints
BEFORE INSERT OR UPDATE ON enhanced_schedules
FOR EACH ROW
EXECUTE FUNCTION check_inspector_schedule_constraints();
