-- Create cronjobs table
CREATE TABLE IF NOT EXISTS cronjobs (
  id SERIAL PRIMARY KEY,
  job_id VARCHAR(100) UNIQUE NOT NULL,
  recipient VARCHAR(255) NOT NULL,
  created_by INTEGER REFERENCES users(id),
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  provider_type VARCHAR(50) NOT NULL CHECK (provider_type IN ('email', 'sms')),
  template_id INTEGER REFERENCES templates(id),
  queued_date TIMESTAMP,
  sent_date TIMESTAMP,
  status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed')),
  last_result TEXT,
  payload JSONB NOT NULL,
  payload_md5 VARCHAR(32) NOT NULL,
  retry_times INTEGER NOT NULL DEFAULT 0,
  max_retries INTEGER NOT NULL DEFAULT 3,
  next_retry_at TIMESTAMP,
  priority INTEGER NOT NULL DEFAULT 0,
  subject <PERSON>RCHA<PERSON>(255),
  category VARCHAR(100)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_cronjobs_status ON cronjobs(status);
CREATE INDEX IF NOT EXISTS idx_cronjobs_job_id ON cronjobs(job_id);
CREATE INDEX IF NOT EXISTS idx_cronjobs_next_retry ON cronjobs(next_retry_at) WHERE status = 'pending';
CREATE INDEX IF NOT EXISTS idx_cronjobs_payload_md5 ON cronjobs(payload_md5);
