import type { NextResponse } from "next/server"
import { authenticateRequest } from "./auth"
import { errorResponse } from "@/lib/api-response"

/**
 * Middleware to check if user has required roles
 * @param allowedRoles Array of roles that are allowed to access the endpoint
 */
export function withRoleCheck(allowedRoles: string[]) {
  return async function roleCheckMiddleware(
    request: Request,
    handler: (request: Request, auth: { user: any }) => Promise<NextResponse>,
  ) {
    // First authenticate the request
    const auth = await authenticateRequest(request)

    // If not authenticated, return unauthorized response
    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Check if user has one of the allowed roles
    if (!allowedRoles.includes(auth.user.role)) {
      return errorResponse(`Access denied. Required roles: ${allowedRoles.join(", ")}`, 403, "FORBIDDEN")
    }

    // User is authenticated and has the required role, proceed with the handler
    return handler(request, auth)
  }
}

/**
 * Middleware to check if user is an admin
 */
export function adminOnly(request: Request, handler: (request: Request, auth: { user: any }) => Promise<NextResponse>) {
  return withRoleCheck(["admin"])(request, handler)
}

/**
 * Middleware to check if user is an admin or regular user
 */
export function userOrAdmin(
  request: Request,
  handler: (request: Request, auth: { user: any }) => Promise<NextResponse>,
) {
  return withRoleCheck(["user", "admin"])(request, handler)
}

/**
 * Middleware to check if user is the owner of the resource or an admin
 * @param getUserIdFromRequest Function to extract the user ID from the request
 */
export function ownerOrAdmin(getUserIdFromRequest: (request: Request) => Promise<number | null>) {
  return async function ownerOrAdminMiddleware(
    request: Request,
    handler: (request: Request, auth: { user: any }) => Promise<NextResponse>,
  ) {
    // First authenticate the request
    const auth = await authenticateRequest(request)

    // If not authenticated, return unauthorized response
    if (!auth.authenticated) {
      return errorResponse(auth.error || "Unauthorized", 401, "UNAUTHORIZED")
    }

    // Admin can access any resource
    if (auth.user.role === "admin") {
      return handler(request, auth)
    }

    // For non-admins, check if they are the owner
    const resourceUserId = await getUserIdFromRequest(request)

    // If we couldn't determine the owner or the user is not the owner
    if (resourceUserId === null || auth.user.userId !== resourceUserId) {
      return errorResponse("You don't have permission to access this resource", 403, "FORBIDDEN")
    }

    // User is the owner, proceed with the handler
    return handler(request, auth)
  }
}
