import { verifyJWT } from "@/lib/jwt"
import { errorResponse } from "@/lib/api-response"

/**
 * Middleware to authenticate requests using JWT
 */
export async function authenticateRequest(request: Request) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get("authorization")

    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return { authenticated: false, error: "Missing or invalid authorization header" }
    }

    // Extract the token
    const token = authHeader.split(" ")[1]

    if (!token) {
      return { authenticated: false, error: "No token provided" }
    }

    // Verify the token
    const payload = await verifyJWT(token)

    return { authenticated: true, user: payload }
  } catch (error) {
    console.error("Authentication error:", error)
    return { authenticated: false, error: "Invalid or expired token" }
  }
}

/**
 * Helper function to create an unauthorized response
 */
export function unauthorizedResponse(message = "Unauthorized") {
  return errorResponse(message, 401, "UNAUTHORIZED")
}

/**
 * Helper function to create a forbidden response
 */
export function forbiddenResponse(message = "Forbidden") {
  return errorResponse(message, 403, "FORBIDDEN")
}
